#!/usr/bin/env python3
"""
Diagnostic script to investigate LAS file parsing issues.
Specifically designed to debug the "Failed to parse data from LAS file" error.
"""

import os
import sys
import re
from pathlib import Path

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from data.las_loader import LASLoader

def diagnose_las_file(file_path):
    """Comprehensive diagnosis of a LAS file parsing issue."""
    print(f"🔍 DIAGNOSING LAS FILE: {os.path.basename(file_path)}")
    print("="*60)
    
    if not os.path.exists(file_path):
        print(f"❌ ERROR: File not found: {file_path}")
        return
    
    # Read raw file content
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        print(f"✅ File read successfully: {len(lines)} lines")
    except Exception as e:
        print(f"❌ ERROR reading file: {e}")
        return
    
    # Analyze file structure
    print(f"\n📋 FILE STRUCTURE ANALYSIS:")
    sections = {}
    current_section = None
    
    for i, line in enumerate(lines):
        line_stripped = line.strip()
        if line_stripped.startswith('~'):
            current_section = line_stripped
            sections[current_section] = i
            print(f"  Line {i+1:4d}: {current_section}")
    
    # Check for required sections
    print(f"\n🔍 SECTION VALIDATION:")
    required_sections = ['~Well', '~Curve', '~ASCII', '~A']
    
    well_section_found = any(s.startswith('~Well') or s.startswith('~W') for s in sections.keys())
    curve_section_found = any(s.startswith('~Curve') or s.startswith('~C') for s in sections.keys())
    data_section_found = any(s.startswith('~ASCII') or s.startswith('~A') for s in sections.keys())
    
    print(f"  Well section (~Well or ~W): {'✅' if well_section_found else '❌'}")
    print(f"  Curve section (~Curve or ~C): {'✅' if curve_section_found else '❌'}")
    print(f"  Data section (~ASCII or ~A): {'✅' if data_section_found else '❌'}")
    
    # Analyze curve information
    print(f"\n📊 CURVE INFORMATION ANALYSIS:")
    curve_info = {}
    in_curve_section = False
    
    for i, line in enumerate(lines):
        line_stripped = line.strip()
        
        if line_stripped.startswith('~Curve') or line_stripped.startswith('~C'):
            in_curve_section = True
            continue
        elif line_stripped.startswith('~'):
            in_curve_section = False
            continue
        
        if in_curve_section and line_stripped and not line_stripped.startswith('#'):
            parts = line_stripped.split(':')
            if len(parts) >= 2:
                curve_part = parts[0].strip()
                desc_part = ':'.join(parts[1:]).strip()
                
                # Extract curve name and unit
                curve_match = re.match(r'(\S+)\s*\.(\S*)', curve_part)
                if curve_match:
                    curve_name = curve_match.group(1)
                    unit = curve_match.group(2)
                    curve_info[curve_name] = {
                        'unit': unit,
                        'description': desc_part,
                        'line': i + 1
                    }
    
    print(f"  Found {len(curve_info)} curves:")
    for curve_name, info in curve_info.items():
        print(f"    {curve_name:15s} | {info['unit']:10s} | {info['description'][:40]}")
    
    # Analyze data section
    print(f"\n📈 DATA SECTION ANALYSIS:")
    in_data_section = False
    data_lines = []
    data_start_line = None
    
    for i, line in enumerate(lines):
        line_stripped = line.strip()
        
        if line_stripped.startswith('~ASCII') or line_stripped.startswith('~A'):
            in_data_section = True
            data_start_line = i + 1
            continue
        elif line_stripped.startswith('~'):
            in_data_section = False
            continue
        
        if in_data_section and line_stripped and not line_stripped.startswith('#'):
            data_lines.append((i + 1, line_stripped))
    
    print(f"  Data section starts at line: {data_start_line}")
    print(f"  Found {len(data_lines)} data lines")
    
    if data_lines:
        print(f"  First few data lines:")
        for i, (line_num, line_content) in enumerate(data_lines[:5]):
            values = line_content.split()
            print(f"    Line {line_num:4d}: {len(values):2d} values | {line_content[:60]}{'...' if len(line_content) > 60 else ''}")
        
        # Check data consistency
        print(f"\n🔍 DATA CONSISTENCY CHECK:")
        expected_columns = len(curve_info)
        print(f"  Expected columns (from curve info): {expected_columns}")
        
        valid_rows = 0
        invalid_rows = 0
        column_counts = {}
        
        for line_num, line_content in data_lines:
            values = line_content.split()
            num_values = len(values)
            
            if num_values == expected_columns:
                valid_rows += 1
            else:
                invalid_rows += 1
            
            column_counts[num_values] = column_counts.get(num_values, 0) + 1
        
        print(f"  Valid rows (matching expected columns): {valid_rows}")
        print(f"  Invalid rows (not matching expected columns): {invalid_rows}")
        print(f"  Column count distribution:")
        for count, frequency in sorted(column_counts.items()):
            status = "✅" if count == expected_columns else "❌"
            print(f"    {count:2d} columns: {frequency:4d} rows {status}")
        
        if invalid_rows > 0:
            print(f"\n⚠️  PROBLEM IDENTIFIED:")
            print(f"  The data lines don't match the expected number of columns!")
            print(f"  Expected: {expected_columns} columns")
            print(f"  This causes the 'Failed to parse data from LAS file' error.")
            
            print(f"\n🔍 SAMPLE INVALID LINES:")
            count = 0
            for line_num, line_content in data_lines:
                values = line_content.split()
                if len(values) != expected_columns and count < 3:
                    print(f"    Line {line_num}: {len(values)} values (expected {expected_columns})")
                    print(f"      Content: {line_content}")
                    count += 1
    else:
        print(f"  ❌ No data lines found!")
        print(f"  This could be due to:")
        print(f"    - Missing ~ASCII or ~A section header")
        print(f"    - All data lines are comments (start with #)")
        print(f"    - Data section is empty")
    
    # Try to load with LASLoader to see the actual error
    print(f"\n🧪 TESTING WITH LASLoader:")
    try:
        loader = LASLoader()
        data = loader.load(file_path)
        print(f"  ✅ SUCCESS: Loaded {data.shape[0]} rows, {data.shape[1]} columns")
    except Exception as e:
        print(f"  ❌ ERROR: {str(e)}")
        print(f"  This confirms the parsing issue.")

def main():
    """Main function to diagnose the problematic LAS file."""
    # Find the problematic LAS file
    las_dir = os.path.join(os.path.dirname(__file__), 'Las')
    problematic_file = os.path.join(las_dir, 'B-G-6_RP_INPUT.las')
    
    if os.path.exists(problematic_file):
        diagnose_las_file(problematic_file)
    else:
        print(f"❌ File not found: {problematic_file}")
        
        # List available LAS files
        if os.path.exists(las_dir):
            las_files = [f for f in os.listdir(las_dir) if f.lower().endswith('.las')]
            print(f"\nAvailable LAS files in {las_dir}:")
            for f in sorted(las_files):
                print(f"  - {f}")
        else:
            print(f"❌ LAS directory not found: {las_dir}")

if __name__ == "__main__":
    main()
