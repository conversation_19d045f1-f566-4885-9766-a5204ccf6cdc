"""
LAS file loader for well log data processing.
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Union
import re
import os


class LASLoader:
    """
    LAS file loader for parsing and extracting well log data.
    Handles LAS format files commonly used in geophysical applications.
    """

    def __init__(self):
        self.well_info = {}
        self.curve_info = {}
        self.data = None
        self.null_value = -999.25  # Initialize as float

    def load(self, file_path: str) -> pd.DataFrame:
        """
        Load and parse a LAS file.

        Args:
            file_path: Path to the LAS file

        Returns:
            DataFrame containing the well log data
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"LAS file not found: {file_path}")

        with open(file_path, 'r') as f:
            lines = f.readlines()

        # Parse different sections
        self._parse_well_info(lines)
        self._parse_curve_info(lines)
        self._parse_data(lines)

        return self.data

    def _parse_well_info(self, lines: List[str]) -> None:
        """Parse well information section."""
        in_well_section = False

        for line in lines:
            line = line.strip()

            if line.startswith('~Well'):
                in_well_section = True
                continue
            elif line.startswith('~'):
                in_well_section = False
                continue

            if in_well_section and line and not line.startswith('#'):
                parts = line.split(':')
                if len(parts) >= 2:
                    key_part = parts[0].strip()
                    value_part = ':'.join(parts[1:]).strip()

                    # Extract key and unit
                    key_match = re.match(r'(\w+)\.?(\w*)?', key_part)
                    if key_match:
                        key = key_match.group(1)
                        unit = key_match.group(2) if key_match.group(2) else ''

                        # Extract value
                        value_match = re.match(r'([^\s:]+)', value_part)
                        if value_match:
                            value = value_match.group(1)
                            try:
                                value = float(value)
                            except ValueError:
                                pass

                            self.well_info[key] = {
                                'value': value,
                                'unit': unit,
                                'description': value_part
                            }

                            # Store null value for data cleaning
                            if key == 'NULL':
                                try:
                                    self.null_value = float(value)
                                except (ValueError, TypeError):
                                    self.null_value = -999.25  # Default null value

    def _parse_curve_info(self, lines: List[str]) -> None:
        """Parse curve information section with enhanced error handling."""
        in_curve_section = False
        curve_section_found = False

        for line in lines:
            line = line.strip()

            if line.startswith('~Curve') or line.startswith('~C'):
                in_curve_section = True
                curve_section_found = True
                continue
            elif line.startswith('~'):
                in_curve_section = False
                continue

            if in_curve_section and line and not line.startswith('#'):
                parts = line.split(':')
                if len(parts) >= 2:
                    curve_part = parts[0].strip()
                    desc_part = ':'.join(parts[1:]).strip()

                    # Extract curve name and unit
                    curve_match = re.match(r'(\S+)\s*\.(\S*)', curve_part)
                    if curve_match:
                        curve_name = curve_match.group(1)
                        unit = curve_match.group(2)

                        self.curve_info[curve_name] = {
                            'unit': unit,
                            'description': desc_part
                        }
                    else:
                        # Try alternative format without unit
                        curve_name = curve_part.strip()
                        if curve_name:
                            self.curve_info[curve_name] = {
                                'unit': '',
                                'description': desc_part
                            }
                            print(f"Warning: Curve '{curve_name}' has no unit specified")

        if not curve_section_found:
            raise ValueError("No curve information section found in LAS file (missing ~Curve or ~C header)")

        if not self.curve_info:
            raise ValueError("No curve information found in LAS file (curve section is empty)")

    def _parse_data(self, lines: List[str]) -> None:
        """Parse data section with enhanced error reporting."""
        in_data_section = False
        data_lines = []
        data_section_found = False

        for i, line in enumerate(lines):
            line = line.strip()

            if line.startswith('~ASCII') or line.startswith('~A'):
                in_data_section = True
                data_section_found = True
                continue
            elif line.startswith('~'):
                in_data_section = False
                continue

            if in_data_section and line and not line.startswith('#'):
                data_lines.append((i + 1, line))  # Store line number for debugging

        if not data_section_found:
            raise ValueError("No data section found in LAS file (missing ~ASCII or ~A header)")

        if not data_lines:
            raise ValueError("No data found in LAS file (data section is empty or all lines are comments)")

        # Enhanced data parsing with better error reporting
        data_rows = []
        expected_columns = len(self.curve_info)
        invalid_lines = []

        for line_num, line_content in data_lines:
            values = line_content.split()

            if len(values) == expected_columns:
                # Convert to numeric, replacing null values with NaN
                numeric_values = []
                for val in values:
                    try:
                        num_val = float(val)
                        # Ensure null_value is numeric for comparison
                        try:
                            null_val = float(self.null_value)
                            if abs(num_val - null_val) < 1e-6:
                                numeric_values.append(np.nan)
                            else:
                                numeric_values.append(num_val)
                        except (ValueError, TypeError):
                            # If null_value conversion fails, use default comparison
                            if abs(num_val - (-999.25)) < 1e-6:
                                numeric_values.append(np.nan)
                            else:
                                numeric_values.append(num_val)
                    except (ValueError, TypeError):
                        numeric_values.append(np.nan)
                data_rows.append(numeric_values)
            else:
                invalid_lines.append((line_num, len(values), line_content[:50]))

        if data_rows:
            self.data = pd.DataFrame(data_rows, columns=list(self.curve_info.keys()))

            # Report any invalid lines as warnings
            if invalid_lines:
                print(f"Warning: Skipped {len(invalid_lines)} invalid data lines:")
                for line_num, num_values, content in invalid_lines[:5]:  # Show first 5
                    print(f"  Line {line_num}: {num_values} values (expected {expected_columns}) - {content}...")
                if len(invalid_lines) > 5:
                    print(f"  ... and {len(invalid_lines) - 5} more invalid lines")
        else:
            # Provide detailed error message
            error_msg = f"Failed to parse data from LAS file:\n"
            error_msg += f"  - Expected {expected_columns} columns (from curve info)\n"
            error_msg += f"  - Found {len(data_lines)} data lines\n"
            error_msg += f"  - None of the data lines have the correct number of columns\n"

            if invalid_lines:
                error_msg += f"  - Column count distribution:\n"
                column_counts = {}
                for _, num_values, _ in invalid_lines:
                    column_counts[num_values] = column_counts.get(num_values, 0) + 1

                for count, frequency in sorted(column_counts.items()):
                    error_msg += f"    {count} columns: {frequency} lines\n"

                error_msg += f"  - Sample invalid lines:\n"
                for line_num, num_values, content in invalid_lines[:3]:
                    error_msg += f"    Line {line_num}: {num_values} values - {content}...\n"

            raise ValueError(error_msg)

    def get_curve_names(self) -> List[str]:
        """Get list of available curve names."""
        return list(self.curve_info.keys()) if self.curve_info else []

    def get_well_info(self) -> Dict:
        """Get well information."""
        return self.well_info

    def get_curve_info(self) -> Dict:
        """Get curve information."""
        return self.curve_info


class WellLogDataProcessor:
    """
    Processor for well log data to prepare it for ML training.
    """

    def __init__(self, target_curves: List[str] = None, feature_curves: List[str] = None):
        """
        Initialize the processor.

        Args:
            target_curves: List of target curve names for prediction
            feature_curves: List of feature curve names for input
        """
        self.target_curves = target_curves or ['S-WAVE', 'DTS']
        self.feature_curves = feature_curves or ['P-WAVE', 'RHOB', 'PHIE', 'RT']

    def process_multiple_wells(self, las_files: List[str]) -> Tuple[np.ndarray, np.ndarray, List[str]]:
        """
        Process multiple LAS files and combine data.

        Args:
            las_files: List of LAS file paths

        Returns:
            Tuple of (features, targets, successful_files)
        """
        all_features = []
        all_targets = []
        successful_files = []

        for las_file in las_files:
            try:
                features, targets = self.process_single_well(las_file)
                if features is not None and targets is not None:
                    all_features.append(features)
                    all_targets.append(targets)
                    successful_files.append(las_file)
                    print(f"Successfully processed: {os.path.basename(las_file)}")
                else:
                    print(f"Warning: No valid data extracted from {os.path.basename(las_file)}")
            except Exception as e:
                print(f"Error processing {os.path.basename(las_file)}: {str(e)}")

        if not all_features:
            raise ValueError("No valid data could be extracted from any LAS files")

        # Combine all data
        combined_features = np.concatenate(all_features, axis=0)
        combined_targets = np.concatenate(all_targets, axis=0)

        return combined_features, combined_targets, successful_files

    def process_single_well(self, las_file: str) -> Tuple[Optional[np.ndarray], Optional[np.ndarray]]:
        """
        Process a single LAS file.

        Args:
            las_file: Path to LAS file

        Returns:
            Tuple of (features, targets) or (None, None) if processing fails
        """
        try:
            loader = LASLoader()
            data = loader.load(las_file)
            
            print(f"    Loaded {data.shape[0]} rows, {data.shape[1]} columns")
            print(f"    Available curves: {list(data.columns)}")
            
        except Exception as e:
            print(f"    Failed to load LAS file: {str(e)}")
            raise

        # Map curve names to standardized names
        curve_mapping = self._get_curve_mapping(loader.get_curve_names())
        print(f"    Curve mapping: {curve_mapping}")

        # Extract features and targets
        features = self._extract_curves(data, self.feature_curves, curve_mapping)
        targets = self._extract_curves(data, self.target_curves, curve_mapping)

        if features is None or targets is None:
            return None, None

        # Clean data with more intelligent handling of missing values
        print(f"    Before cleaning - Features: {features.shape}, Targets: {targets.shape}")

        # Check missing values per column
        feature_nan_counts = np.isnan(features).sum(axis=0)
        target_nan_counts = np.isnan(targets).sum(axis=0)

        print(f"    Feature NaN counts: {feature_nan_counts}")
        print(f"    Target NaN counts: {target_nan_counts}")

        # Strategy 1: Remove rows where ALL features OR ALL targets are NaN
        all_features_nan = np.isnan(features).all(axis=1)
        all_targets_nan = np.isnan(targets).all(axis=1)
        completely_invalid_mask = all_features_nan | all_targets_nan

        # Strategy 2: Remove rows where ANY target is NaN (targets are critical)
        any_target_nan = np.isnan(targets).any(axis=1)

        # Strategy 3: For features, allow some missing values but not too many
        # Remove rows where more than 50% of features are missing
        feature_nan_ratio = np.isnan(features).sum(axis=1) / features.shape[1]
        too_many_feature_nans = feature_nan_ratio > 0.5

        # Combine all invalid conditions
        invalid_mask = completely_invalid_mask | any_target_nan | too_many_feature_nans
        valid_mask = ~invalid_mask

        print(f"    Rows with all features NaN: {all_features_nan.sum()}")
        print(f"    Rows with all targets NaN: {all_targets_nan.sum()}")
        print(f"    Rows with any target NaN: {any_target_nan.sum()}")
        print(f"    Rows with >50% features NaN: {too_many_feature_nans.sum()}")
        print(f"    Total invalid rows: {invalid_mask.sum()}")
        print(f"    Valid rows remaining: {valid_mask.sum()} ({valid_mask.sum()/len(features)*100:.1f}%)")

        if not np.any(valid_mask):
            print("    Warning: No valid rows remaining after cleaning")
            return None, None

        features_clean = features[valid_mask]
        targets_clean = targets[valid_mask]

        # Apply simple forward-fill interpolation for remaining NaN values in features
        for i in range(features_clean.shape[1]):
            col = features_clean[:, i]
            if np.isnan(col).any():
                # Forward fill
                mask = np.isnan(col)
                col[mask] = np.interp(np.flatnonzero(mask), np.flatnonzero(~mask), col[~mask])
                features_clean[:, i] = col
                print(f"    Applied interpolation to feature column {i}")

        print(f"    After cleaning - Features: {features_clean.shape}, Targets: {targets_clean.shape}")
        return features_clean, targets_clean

    def _get_curve_mapping(self, available_curves: List[str]) -> Dict[str, str]:
        """
        Create mapping from standard names to actual curve names in the LAS file.

        Args:
            available_curves: List of curve names available in the LAS file

        Returns:
            Dictionary mapping standard names to actual curve names
        """
        mapping = {}

        # Common mappings for different naming conventions
        standard_mappings = {
            'P-WAVE': ['P-WAVE', 'PVEL', 'VP', 'P_WAVE', 'PVEL_WET'],
            'S-WAVE': ['S-WAVE', 'SVEL', 'VS', 'S_WAVE', 'SVEL_WET'],
            'RHOB': ['RHOB', 'DENS', 'DENSITY', 'DENS_WET'],
            'PHIE': ['PHIE', 'PHI', 'POROSITY', 'PORO'],
            'RT': ['RT', 'RES', 'RESISTIVITY', 'RESIS'],
            'DT': ['DT', 'SLOWNESS_P'],
            'DTS': ['DTS', 'SLOWNESS_S']
        }

        for standard_name, possible_names in standard_mappings.items():
            for possible_name in possible_names:
                if possible_name in available_curves:
                    mapping[standard_name] = possible_name
                    break

        return mapping

    def _extract_curves(self, data: pd.DataFrame, curve_names: List[str],
                       curve_mapping: Dict[str, str]) -> Optional[np.ndarray]:
        """
        Extract specific curves from the data.

        Args:
            data: DataFrame containing well log data
            curve_names: List of standard curve names to extract
            curve_mapping: Mapping from standard names to actual column names

        Returns:
            Numpy array of extracted curves or None if not enough curves found
        """
        extracted_columns = []

        for curve_name in curve_names:
            if curve_name in curve_mapping:
                actual_name = curve_mapping[curve_name]
                if actual_name in data.columns:
                    extracted_columns.append(data[actual_name].values)
                else:
                    print(f"Warning: Curve {actual_name} not found in data columns")
                    return None
            else:
                print(f"Warning: No mapping found for curve {curve_name}")
                return None

        if len(extracted_columns) == len(curve_names):
            return np.column_stack(extracted_columns)
        else:
            return None

    def create_sequences(self, features: np.ndarray, targets: np.ndarray,
                        sequence_length: int = 50, stride: int = 1) -> Tuple[np.ndarray, np.ndarray]:
        """
        Create sequences from the data for RNN training.

        Args:
            features: Feature array of shape (n_samples, n_features)
            targets: Target array of shape (n_samples, n_targets)
            sequence_length: Length of each sequence
            stride: Stride between sequences

        Returns:
            Tuple of (sequence_features, sequence_targets)
        """
        if len(features) < sequence_length:
            raise ValueError(f"Data length ({len(features)}) is less than sequence length ({sequence_length})")

        n_sequences = (len(features) - sequence_length) // stride + 1

        seq_features = np.zeros((n_sequences, sequence_length, features.shape[1]))
        seq_targets = np.zeros((n_sequences, sequence_length, targets.shape[1]))

        for i in range(n_sequences):
            start_idx = i * stride
            end_idx = start_idx + sequence_length
            seq_features[i] = features[start_idx:end_idx]
            seq_targets[i] = targets[start_idx:end_idx]

        return seq_features, seq_targets