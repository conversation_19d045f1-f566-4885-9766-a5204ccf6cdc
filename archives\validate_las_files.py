#!/usr/bin/env python3
"""
Comprehensive LAS file validator to diagnose parsing issues.
This script checks all LAS files and provides detailed diagnostics.
"""

import os
import sys
from pathlib import Path

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from data.las_loader import LASLoader

def validate_single_las_file(file_path):
    """Validate a single LAS file and return detailed results."""
    results = {
        'file_name': os.path.basename(file_path),
        'file_path': file_path,
        'status': 'unknown',
        'error': None,
        'warnings': [],
        'data_shape': None,
        'curves': [],
        'depth_range': None,
        'data_quality': None
    }
    
    try:
        # Test loading with enhanced LASLoader
        loader = LASLoader()
        data = loader.load(file_path)
        
        results['status'] = 'success'
        results['data_shape'] = data.shape
        results['curves'] = list(data.columns)
        
        # Calculate depth range if DEPTH column exists
        if 'DEPTH' in data.columns:
            depth_col = data['DEPTH']
            results['depth_range'] = (depth_col.min(), depth_col.max())
        
        # Calculate data quality (percentage of non-null values)
        total_cells = data.shape[0] * data.shape[1]
        non_null_cells = data.count().sum()
        results['data_quality'] = (non_null_cells / total_cells) * 100 if total_cells > 0 else 0
        
        # Check for required curves
        required_features = ['P-WAVE', 'RHOB', 'PHIE', 'RT']
        required_targets = ['S-WAVE']
        all_required = required_features + required_targets
        
        missing_curves = []
        for curve in all_required:
            if curve not in results['curves']:
                missing_curves.append(curve)
        
        if missing_curves:
            results['warnings'].append(f"Missing required curves: {missing_curves}")
        
    except Exception as e:
        results['status'] = 'error'
        results['error'] = str(e)
    
    return results

def validate_all_las_files():
    """Validate all LAS files in the Las directory."""
    print("🔍 LAS FILE VALIDATION REPORT")
    print("="*60)
    
    # Find LAS directory
    las_dir = os.path.join(os.path.dirname(__file__), 'Las')
    if not os.path.exists(las_dir):
        print(f"❌ ERROR: LAS directory not found: {las_dir}")
        return
    
    # Find all LAS files
    las_files = [f for f in os.listdir(las_dir) if f.lower().endswith('.las')]
    if not las_files:
        print(f"❌ ERROR: No LAS files found in {las_dir}")
        return
    
    print(f"📁 Found {len(las_files)} LAS files in {las_dir}")
    print()
    
    # Validate each file
    results = []
    successful_files = []
    failed_files = []
    
    for las_file in sorted(las_files):
        file_path = os.path.join(las_dir, las_file)
        print(f"🔍 Validating: {las_file}")
        
        result = validate_single_las_file(file_path)
        results.append(result)
        
        if result['status'] == 'success':
            successful_files.append(result)
            print(f"  ✅ SUCCESS: {result['data_shape'][0]:,} rows, {result['data_shape'][1]} columns")
            if result['depth_range']:
                print(f"     Depth: {result['depth_range'][0]:.1f} - {result['depth_range'][1]:.1f}m")
            print(f"     Quality: {result['data_quality']:.1f}%")
            if result['warnings']:
                for warning in result['warnings']:
                    print(f"     ⚠️  {warning}")
        else:
            failed_files.append(result)
            print(f"  ❌ FAILED: {result['error']}")
        print()
    
    # Summary report
    print("📊 VALIDATION SUMMARY")
    print("="*60)
    print(f"Total files: {len(las_files)}")
    print(f"Successful: {len(successful_files)} ✅")
    print(f"Failed: {len(failed_files)} ❌")
    print()
    
    if successful_files:
        print("✅ SUCCESSFUL FILES:")
        for result in successful_files:
            curves_info = f"{len(result['curves'])} curves"
            quality_info = f"{result['data_quality']:.1f}% quality"
            print(f"  • {result['file_name']:<25} | {result['data_shape'][0]:>6,} rows | {curves_info:<12} | {quality_info}")
        print()
    
    if failed_files:
        print("❌ FAILED FILES:")
        for result in failed_files:
            print(f"  • {result['file_name']:<25} | {result['error']}")
        print()
        
        print("🔧 TROUBLESHOOTING FAILED FILES:")
        print("-"*40)
        for result in failed_files:
            print(f"\n📋 {result['file_name']}:")
            error = result['error']
            
            if "No data section found" in error:
                print("  🔍 Issue: Missing data section header")
                print("  💡 Solution: Check if file has ~ASCII or ~A section")
                
            elif "No curve information section found" in error:
                print("  🔍 Issue: Missing curve information section")
                print("  💡 Solution: Check if file has ~Curve or ~C section")
                
            elif "Failed to parse data from LAS file" in error:
                print("  🔍 Issue: Data format mismatch")
                print("  💡 Solution: Number of data columns doesn't match curve definitions")
                print("  📝 Details:")
                # Extract details from enhanced error message
                error_lines = error.split('\n')
                for line in error_lines:
                    if line.strip().startswith('-'):
                        print(f"    {line.strip()}")
                
            elif "No data found in LAS file" in error:
                print("  🔍 Issue: Empty data section")
                print("  💡 Solution: Check if data section contains actual data")
                
            else:
                print(f"  🔍 Issue: {error}")
                print("  💡 Solution: Manual inspection required")
    
    # Curve availability analysis
    if successful_files:
        print("📊 CURVE AVAILABILITY ANALYSIS:")
        print("-"*40)
        
        required_curves = ['P-WAVE', 'RHOB', 'PHIE', 'RT', 'S-WAVE']
        curve_availability = {}
        
        for curve in required_curves:
            available_count = sum(1 for result in successful_files if curve in result['curves'])
            curve_availability[curve] = available_count
        
        for curve, count in curve_availability.items():
            percentage = (count / len(successful_files)) * 100
            status = "✅" if percentage == 100 else "⚠️" if percentage >= 50 else "❌"
            print(f"  {curve:<10} | {count:2d}/{len(successful_files)} files ({percentage:5.1f}%) {status}")
        
        # Files usable for training
        usable_files = []
        for result in successful_files:
            has_all_required = all(curve in result['curves'] for curve in required_curves)
            if has_all_required:
                usable_files.append(result)
        
        print(f"\n🎯 TRAINING READINESS:")
        print(f"  Files with all required curves: {len(usable_files)}/{len(successful_files)}")
        if usable_files:
            print("  Usable files:")
            for result in usable_files:
                print(f"    • {result['file_name']} ({result['data_quality']:.1f}% quality)")

def main():
    """Main function."""
    validate_all_las_files()

if __name__ == "__main__":
    main()
