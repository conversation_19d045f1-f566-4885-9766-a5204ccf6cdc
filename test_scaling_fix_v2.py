#!/usr/bin/env python3
"""
Test script to verify the corrected scaling/unit conversion pipeline.
This tests the complete denormalization pipeline to ensure BiGRU predictions
are in the correct physical units.
"""
import sys
import os
import yaml
import torch
import numpy as np

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from models.neural_networks import BiGRU
from models.rock_physics import RockPhysicsFactory
from training.strategies import PhysicsGuidanceStrategy, StrategyHandler
from training.trainer import PhysicsGuidedTrainer
from data.preprocessing import WellLogPreprocessor

def test_corrected_scaling():
    """Test the corrected scaling and unit conversion pipeline."""
    print("Testing CORRECTED Scaling Fix: Unit Conversion Pipeline")
    print("=" * 70)
    
    # Load config
    config_path = "configs/default_config.yaml"
    with open(config_path, "r") as f:
        config = yaml.safe_load(f)
    
    # Create realistic well log data (in physical units)
    n_samples = 50
    seq_len = 30
    n_features = len(config['data']['features'])
    
    np.random.seed(42)
    
    # P-WAVE: 2000-6000 m/s (realistic range)
    p_wave = np.random.uniform(2000, 6000, (n_samples, seq_len))
    
    # RHOB: 1.8-2.8 g/cm³
    rhob = np.random.uniform(1.8, 2.8, (n_samples, seq_len))
    
    # PHIE: 0.05-0.35 (porosity)
    phie = np.random.uniform(0.05, 0.35, (n_samples, seq_len))
    
    # RT: 0.1-1000 ohm-m
    rt = np.random.lognormal(1, 2, (n_samples, seq_len))
    rt = np.clip(rt, 0.1, 1000)
    
    # S-WAVE: Use mudrock line as ground truth (with noise)
    p_wave_kms = p_wave / 1000.0
    s_wave_kms = (p_wave_kms - 1.36) / 1.16  # Mudrock line
    s_wave_kms += np.random.normal(0, 0.05, s_wave_kms.shape)  # Small noise
    s_wave_kms = np.clip(s_wave_kms, 0.5, 4.0)
    s_wave = s_wave_kms * 1000.0  # Convert back to m/s
    
    # Combine features
    features = np.stack([p_wave, rhob, phie, rt], axis=-1)
    targets = s_wave[..., np.newaxis]
    
    print(f"✓ Created realistic data:")
    print(f"  P-WAVE range: {p_wave.min():.0f}-{p_wave.max():.0f} m/s")
    print(f"  S-WAVE range: {s_wave.min():.0f}-{s_wave.max():.0f} m/s")
    
    # Preprocess data
    preprocessor = WellLogPreprocessor(
        normalization=config['data']['normalization'],
        feature_engineering=True
    )
    
    features_norm, targets_norm = preprocessor.fit_transform(
        features.reshape(-1, n_features), 
        targets.reshape(-1, 1),
        feature_names=config['data']['features'],
        target_names=config['data']['target']
    )
    
    # Reshape back to sequences
    features_norm = features_norm.reshape(n_samples, seq_len, -1)
    targets_norm = targets_norm.reshape(n_samples, seq_len, 1)
    
    print(f"✓ Data preprocessed:")
    print(f"  Normalized features shape: {features_norm.shape}")
    print(f"  Feature range after normalization: {features_norm.min():.3f} to {features_norm.max():.3f}")
    
    # Update model input dimension
    actual_input_dim = features_norm.shape[-1]
    config['model']['params']['input_dim'] = actual_input_dim
    
    # Create models
    rock_physics_model = RockPhysicsFactory.create(
        config['rock_physics']['model_type'],
        **config['rock_physics']['params']
    )
    
    strategy = PhysicsGuidanceStrategy(config['training']['strategy'])
    strategy_handler = StrategyHandler(strategy, rock_physics_model)
    
    model = BiGRU(**config['model']['params'])
    
    # Create trainer with preprocessor
    trainer = PhysicsGuidedTrainer(
        model=model,
        rock_physics_model=rock_physics_model,
        strategy_handler=strategy_handler,
        config=config,
        preprocessor=preprocessor
    )
    
    print(f"✓ Trainer created with preprocessor")
    
    # Test the unit conversion during training
    features_tensor = torch.FloatTensor(features_norm[:5])  # First 5 samples
    targets_tensor = torch.FloatTensor(targets_norm[:5])
    
    # Quick training test
    optimizer = torch.optim.Adam(model.parameters(), lr=0.01)
    loss_fn = strategy_handler.get_loss_function()
    
    print("✓ Testing unit conversion during training...")
    
    # Single training step to test unit conversion
    model.train()
    optimizer.zero_grad()
    
    features_batch = features_tensor.to(trainer.device)
    targets_batch = targets_tensor.to(trainer.device)
    
    # Forward pass (this will test our corrected unit conversion)
    predictions = model(features_batch)
    
    # Test physics prediction calculation
    vp_normalized = features_batch[:, :, trainer.vp_index]
    print(f"  VP normalized range: {vp_normalized.min():.3f} to {vp_normalized.max():.3f}")
    
    # The trainer should now properly denormalize VP before physics calculations
    # Let's manually verify this works
    if trainer.preprocessor is not None:
        batch_size, seq_len = vp_normalized.shape
        vp_reshaped = vp_normalized.cpu().numpy().reshape(-1, 1)
        
        # Create dummy feature array
        n_base_features = len(config['data']['features'])
        dummy_features = np.zeros((vp_reshaped.shape[0], n_base_features))
        dummy_features[:, trainer.vp_index] = vp_reshaped.flatten()
        
        # Denormalize
        vp_denorm = trainer.preprocessor.scalers['features'].inverse_transform(dummy_features)
        vp_physical_ms = vp_denorm[:, trainer.vp_index].reshape(batch_size, seq_len)
        vp_physics_kms = vp_physical_ms / 1000.0
        
        print(f"  VP denormalized (m/s): {vp_physical_ms.min():.0f} to {vp_physical_ms.max():.0f}")
        print(f"  VP for physics (km/s): {vp_physics_kms.min():.3f} to {vp_physics_kms.max():.3f}")
        
        # Test physics prediction
        physics_pred_sample = rock_physics_model.predict(vp_physics_kms[0, :5])
        print(f"  Physics prediction sample (km/s): {physics_pred_sample.min():.3f} to {physics_pred_sample.max():.3f}")
        physics_pred_ms = physics_pred_sample * 1000.0
        print(f"  Physics prediction sample (m/s): {physics_pred_ms.min():.0f} to {physics_pred_ms.max():.0f}")
    
    # Calculate loss (this tests the complete pipeline)
    physics_pred = None
    if trainer.strategy_handler.strategy.value == "loss_function":
        # This will use our corrected unit conversion
        vp_normalized = features_batch[:, :, trainer.vp_index]
        
        # The trainer's corrected logic
        if trainer.preprocessor is not None:
            batch_size, seq_len = vp_normalized.shape
            vp_reshaped = vp_normalized.cpu().numpy().reshape(-1, 1)
            n_features = len(trainer.config["data"]["features"])
            dummy_features = np.zeros((vp_reshaped.shape[0], n_features))
            dummy_features[:, trainer.vp_index] = vp_reshaped.flatten()
            vp_denorm = trainer.preprocessor.scalers['features'].inverse_transform(dummy_features)
            vp_physical_ms = vp_denorm[:, trainer.vp_index].reshape(batch_size, seq_len)
            vp_physics_kms = vp_physical_ms / 1000.0
        
        physics_pred = rock_physics_model.predict(vp_physics_kms)
        physics_pred = torch.tensor(physics_pred, device=trainer.device).unsqueeze(-1)
    
    loss = loss_fn(predictions, targets_batch, physics_pred)
    loss.backward()
    optimizer.step()
    
    print(f"✓ Training step completed, loss: {loss.item():.4f}")
    
    # Test prediction and denormalization
    model.eval()
    with torch.no_grad():
        test_predictions_norm = model(features_tensor[:3].to(trainer.device)).cpu().numpy()
    
    # Denormalize predictions using preprocessor
    test_predictions_phys = preprocessor.inverse_transform_targets(test_predictions_norm)
    test_targets_phys = preprocessor.inverse_transform_targets(targets_tensor[:3].cpu().numpy())
    
    print(f"✓ Prediction test:")
    print(f"  BiGRU predictions (m/s): {test_predictions_phys.min():.0f} to {test_predictions_phys.max():.0f}")
    print(f"  Actual targets (m/s): {test_targets_phys.min():.0f} to {test_targets_phys.max():.0f}")
    
    # Check if scaling is reasonable
    pred_mean = np.mean(np.abs(test_predictions_phys))
    target_mean = np.mean(np.abs(test_targets_phys))
    scale_ratio = pred_mean / target_mean
    
    print(f"  Prediction/Target ratio: {scale_ratio:.2f}")
    
    # Test results
    scaling_reasonable = (0.1 < scale_ratio < 10.0)
    predictions_in_range = (500 < test_predictions_phys.max() < 5000)
    
    print(f"\n{'='*70}")
    print("CORRECTED SCALING FIX TEST RESULTS:")
    print(f"✓ Proper denormalization pipeline: {trainer.preprocessor is not None}")
    print(f"✓ VP unit conversion: normalized → m/s → km/s for physics")
    print(f"✓ BiGRU predictions in realistic range: {predictions_in_range}")
    print(f"✓ Scaling ratio reasonable: {scaling_reasonable}")
    
    success = scaling_reasonable and predictions_in_range
    
    if success:
        print("\n🎉 SUCCESS: Corrected scaling pipeline is working!")
        print("   - BiGRU predictions are properly denormalized to physical units")
        print("   - Physics model receives correctly converted VP values")
        print("   - Unit conversion pipeline is now correct")
    else:
        print("\n❌ ISSUE: Scaling problems still exist")
        print("   - Check denormalization logic")
        print("   - Verify preprocessor integration")
    
    return success

if __name__ == "__main__":
    try:
        success = test_corrected_scaling()
        if success:
            print("\n✅ Corrected scaling fix verification PASSED!")
        else:
            print("\n⚠️  Corrected scaling fix needs more work")
            sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
