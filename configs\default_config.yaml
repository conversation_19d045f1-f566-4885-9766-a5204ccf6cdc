model:
  type: "BiGRU"
  params:
    input_dim: 4  # P-WAVE, <PERSON><PERSON><PERSON><PERSON>, P<PERSON>IE, RT
    hidden_dim: 16
    output_dim: 1
    num_layers: 1
    dropout: 0.1

rock_physics:
  model_type: "mudrock_line"
  params:
    a: 1.16
    b: 1.36

training:
  strategy: "pseudolabels"  # or "loss_function", "transfer_learning"
  batch_size: 32
  learning_rate: 0.001
  epochs: 100
  early_stopping_patience: 10

data:
  features: ["P-WAVE", "RHOB", "PHIE", "RT"]
  target: ["S-WAVE"]
  vp_feature_name: "P-WAVE"  # Simple VP identification
  apply_log_transform: ["RT"]  # Simple list of features to log-transform
  train_test_split: 0.8
  normalization: "standard"  # Changed from minmax to standard for stability
  normalization_params:
    type: "standard"  # Options: minmax, standard, robust
    feature_range: [-1, 1]  # Only used for minmax
  sequence_length: 100  # Longer sequences for better pattern learning
  sequence_stride: 10   # Smaller stride for better data utilization

physics_coupling:
  vp_units: "m/s"  # Current VP units in data
  physics_units: "km/s"  # Units expected by physics models
  conversion_factor: 0.001  # Simple conversion factor
