import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from typing import Dict, Any, Optional
import numpy as np
from tqdm import tqdm

class PhysicsGuidedTrainer:
    """
    Trainer for physics-guided neural networks.
    """

    def __init__(
        self,
        model: nn.Module,
        rock_physics_model,
        strategy_handler,
        config: Dict[str, Any],
        device: str = "cuda" if torch.cuda.is_available() else "cpu"
    ):
        self.model = model.to(device)
        self.rock_physics_model = rock_physics_model
        self.strategy_handler = strategy_handler
        self.device = device
        self.config = config

        # Get VP feature index from config
        feature_names = config["data"]["features"]
        vp_feature_name = config["data"].get("vp_feature_name", "P-WAVE")
        self.vp_index = feature_names.index(vp_feature_name)

    def train_epoch(
        self,
        dataloader: DataLoader,
        optimizer: torch.optim.Optimizer,
        loss_fn: nn.Module
    ) -> float:
        """Train for one epoch."""
        self.model.train()
        total_loss = 0.0

        for batch in tqdm(dataloader, desc="Training"):
            features, targets = batch
            features = features.to(self.device)
            targets = targets.to(self.device)

            # Forward pass
            predictions = self.model(features)

            # Calculate physics predictions if needed
            physics_pred = None
            if self.strategy_handler.strategy.value == "loss_function":
                # Use config-driven VP index instead of hard-coded
                vp = features[:, :, self.vp_index]

                # Simple unit conversion (m/s to km/s)
                conversion_factor = self.config["physics_coupling"].get("conversion_factor", 0.001)
                vp_physics = vp * conversion_factor

                # Calculate physics predictions
                physics_pred = self.rock_physics_model.predict(
                    vp_physics.cpu().numpy()
                )
                physics_pred = torch.tensor(
                    physics_pred,
                    device=self.device
                ).unsqueeze(-1)

            # Calculate loss
            if self.strategy_handler.strategy.value == "loss_function":
                loss = loss_fn(predictions, targets, physics_pred)
            else:
                loss = loss_fn(predictions, targets)

            # Backward pass
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()

            total_loss += loss.item()

        return total_loss / len(dataloader)

    def evaluate(
        self,
        dataloader: DataLoader,
        loss_fn: nn.Module
    ) -> Dict[str, float]:
        """Evaluate model performance."""
        self.model.eval()
        total_loss = 0.0
        predictions_list = []
        targets_list = []

        with torch.no_grad():
            for batch in dataloader:
                features, targets = batch
                features = features.to(self.device)
                targets = targets.to(self.device)

                predictions = self.model(features)
                loss = loss_fn(predictions, targets)

                total_loss += loss.item()
                predictions_list.append(predictions.cpu().numpy())
                targets_list.append(targets.cpu().numpy())

        # Calculate metrics
        predictions = np.concatenate(predictions_list)
        targets = np.concatenate(targets_list)

        rmse = np.sqrt(np.mean((predictions - targets) ** 2))
        correlation = np.corrcoef(predictions.flatten(), targets.flatten())[0, 1]

        return {
            "loss": total_loss / len(dataloader),
            "rmse": rmse,
            "correlation": correlation
        }

    def evaluate_model(self, model, test_features, test_targets):
        """Evaluate model on test data with physical unit conversion."""
        with torch.no_grad():
            predictions = model(test_features)
            predictions = predictions.cpu().numpy()

        targets = test_targets.cpu().numpy()

        # Convert to physical units (km/s) for evaluation
        conversion_factor = self.config["physics_coupling"].get("conversion_factor", 0.001)
        predictions_physical = predictions / conversion_factor  # Convert back to km/s
        targets_physical = targets / conversion_factor

        # Calculate metrics in physical units
        rmse = np.sqrt(np.mean((predictions_physical - targets_physical) ** 2))
        correlation = np.corrcoef(predictions_physical.flatten(), targets_physical.flatten())[0, 1]

        return {
            "rmse": rmse,
            "correlation": correlation,
            "predictions_physical": predictions_physical,
            "targets_physical": targets_physical
        }
