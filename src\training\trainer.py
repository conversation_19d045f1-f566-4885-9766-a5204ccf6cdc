import torch
import torch.nn as nn
from torch.utils.data import Data<PERSON>oader
from typing import Dict, Any, Optional
import numpy as np
from tqdm import tqdm

class PhysicsGuidedTrainer:
    """
    Trainer for physics-guided neural networks.
    """

    def __init__(
        self,
        model: nn.Module,
        rock_physics_model,
        strategy_handler,
        config: Dict[str, Any],
        preprocessor=None,
        device: str = "cuda" if torch.cuda.is_available() else "cpu"
    ):
        self.model = model.to(device)
        self.rock_physics_model = rock_physics_model
        self.strategy_handler = strategy_handler
        self.device = device
        self.config = config
        self.preprocessor = preprocessor

        # Get VP feature index from config
        feature_names = config["data"]["features"]
        vp_feature_name = config["data"].get("vp_feature_name", "P-WAVE")
        self.vp_index = feature_names.index(vp_feature_name)

    def train_epoch(
        self,
        dataloader: DataLoader,
        optimizer: torch.optim.Optimizer,
        loss_fn: nn.Module
    ) -> float:
        """Train for one epoch."""
        self.model.train()
        total_loss = 0.0

        for batch in tqdm(dataloader, desc="Training"):
            features, targets = batch
            features = features.to(self.device)
            targets = targets.to(self.device)

            # Forward pass
            predictions = self.model(features)

            # Calculate physics predictions if needed
            physics_pred = None
            if self.strategy_handler.strategy.value == "loss_function":
                # Use config-driven VP index instead of hard-coded
                vp_normalized = features[:, :, self.vp_index]

                # Proper unit conversion: denormalize first, then convert to km/s
                if self.preprocessor is not None and hasattr(self.preprocessor, 'scalers') and 'features' in self.preprocessor.scalers:
                    # Denormalize VP to physical units (m/s)
                    batch_size, seq_len = vp_normalized.shape
                    vp_reshaped = vp_normalized.cpu().numpy().reshape(-1, 1)

                    # Create a dummy feature array with VP in the correct position
                    n_features = len(self.config["data"]["features"])
                    dummy_features = np.zeros((vp_reshaped.shape[0], n_features))
                    dummy_features[:, self.vp_index] = vp_reshaped.flatten()

                    # Denormalize
                    vp_denorm = self.preprocessor.scalers['features'].inverse_transform(dummy_features)
                    vp_physical_ms = vp_denorm[:, self.vp_index].reshape(batch_size, seq_len)

                    # Convert m/s to km/s for physics model (use config conversion factor when provided)
                    conversion_factor = self.config.get("physics_coupling", {}).get("conversion_factor", 0.001)
                    vp_physics_kms = vp_physical_ms * conversion_factor
                else:
                    # Fallback: assume normalized values represent typical VP range
                    vp_min, vp_max = 2000.0, 6000.0  # Typical VP range in m/s
                    vp_physical_ms = vp_normalized.cpu().numpy() * (vp_max - vp_min) + vp_min
                    conversion_factor = self.config.get("physics_coupling", {}).get("conversion_factor", 0.001)
                    vp_physics_kms = vp_physical_ms * conversion_factor

                # Calculate physics predictions (returns VS in km/s for mudrock-based models)
                physics_pred_kms = self.rock_physics_model.predict(vp_physics_kms)

                # Convert physics predictions to m/s to match typical target units
                physics_pred_ms = physics_pred_kms * 1000.0

                # If targets are normalized, normalize physics_pred accordingly for a consistent loss scale
                if self.preprocessor is not None and hasattr(self.preprocessor, 'scalers') and 'targets' in self.preprocessor.scalers:
                    pp_target_scaler = self.preprocessor.scalers['targets']
                    bsz, seqlen = physics_pred_ms.shape
                    physics_pred_ms_flat = physics_pred_ms.reshape(-1, 1)
                    physics_pred_norm_flat = pp_target_scaler.transform(physics_pred_ms_flat)
                    physics_pred_tensor = torch.tensor(
                        physics_pred_norm_flat.reshape(bsz, seqlen, 1),
                        device=self.device,
                        dtype=features.dtype
                    )
                else:
                    # Fall back to raw m/s tensor if no scaler available
                    physics_pred_tensor = torch.tensor(
                        physics_pred_ms,
                        device=self.device,
                        dtype=features.dtype
                    ).unsqueeze(-1)

                physics_pred = physics_pred_tensor

            # Calculate loss
            if self.strategy_handler.strategy.value == "loss_function":
                loss = loss_fn(predictions, targets, physics_pred)
            else:
                loss = loss_fn(predictions, targets)

            # Backward pass
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()

            total_loss += loss.item()

        return total_loss / len(dataloader)

    def evaluate(
        self,
        dataloader: DataLoader,
        loss_fn: nn.Module
    ) -> Dict[str, float]:
        """Evaluate model performance."""
        self.model.eval()
        total_loss = 0.0
        predictions_list = []
        targets_list = []

        with torch.no_grad():
            for batch in dataloader:
                features, targets = batch
                features = features.to(self.device)
                targets = targets.to(self.device)

                predictions = self.model(features)
                loss = loss_fn(predictions, targets)

                total_loss += loss.item()
                predictions_list.append(predictions.cpu().numpy())
                targets_list.append(targets.cpu().numpy())

        # Calculate metrics
        predictions = np.concatenate(predictions_list)
        targets = np.concatenate(targets_list)

        rmse = np.sqrt(np.mean((predictions - targets) ** 2))
        correlation = np.corrcoef(predictions.flatten(), targets.flatten())[0, 1]

        return {
            "loss": total_loss / len(dataloader),
            "rmse": rmse,
            "correlation": correlation
        }


