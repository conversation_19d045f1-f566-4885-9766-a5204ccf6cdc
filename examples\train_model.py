import yaml
import torch
from torch.utils.data import <PERSON><PERSON><PERSON>der, TensorDataset
import numpy as np
import os

from src.models.neural_networks import BiGRU
from src.models.rock_physics import RockPhysicsFactory
from src.training.strategies import PhysicsGuidanceStrategy, StrategyHandler
from src.training.trainer import PhysicsGuidedTrainer

def main():
    """Main function to run the training pipeline."""
    # Load configuration
    config_path = os.path.join(os.path.dirname(__file__), '..', 'configs', 'default_config.yaml')
    with open(config_path, "r") as f:
        config = yaml.safe_load(f)

    print("Configuration loaded.")

    # Load and prepare data (using synthetic data for this example)
    print("Generating synthetic data...")
    n_samples = 500
    seq_len = 50
    # Use input_dim from config
    n_features = config["model"]["params"]["input_dim"]

    features = np.random.randn(n_samples, seq_len, n_features).astype(np.float32)
    targets = np.random.randn(n_samples, seq_len, 1).astype(np.float32)

    # Ensure VP is in a realistic range for the physics model
    features[:, :, 2] = 2.0 + 3.0 * np.random.rand(n_samples, seq_len)

    print(f"Generated data with shape: {features.shape}")

    # Create rock physics model
    rock_physics_model = RockPhysicsFactory.create(
        config["rock_physics"]["model_type"],
        **config["rock_physics"]["params"]
    )
    print(f"Rock physics model '{config['rock_physics']['model_type']}' created.")

    # Create strategy handler
    strategy = PhysicsGuidanceStrategy(config["training"]["strategy"])
    strategy_handler = StrategyHandler(strategy, rock_physics_model)
    print(f"Using guidance strategy: '{config['training']['strategy']}'")

    # Prepare features based on strategy
    if strategy == PhysicsGuidanceStrategy.PSEUDOLABELS:
        print("Adding physics-based pseudolabels as features...")
        # This part needs careful handling of dimensions
        vp = features[:, :, 2]  # VP is at index 2
        vs_physics = np.array([
            rock_physics_model.predict(vp[i]) for i in range(len(vp))
        ]).astype(np.float32)

        features = np.concatenate([
            features,
            vs_physics.reshape(features.shape[0], features.shape[1], 1)
        ], axis=-1)
        # Update input dimension in config
        config["model"]["params"]["input_dim"] += 1
        print(f"New feature shape: {features.shape}")

    # Create neural network
    model = BiGRU(**config["model"]["params"])
    print("Bi-GRU model created.")

    # Create data loaders
    dataset = TensorDataset(
        torch.FloatTensor(features),
        torch.FloatTensor(targets)
    )
    train_size = int(config["data"]["train_test_split"] * len(dataset))
    val_size = len(dataset) - train_size
    train_dataset, val_dataset = torch.utils.data.random_split(
        dataset, [train_size, val_size]
    )

    train_loader = DataLoader(
        train_dataset,
        batch_size=config["training"]["batch_size"],
        shuffle=True
    )
    val_loader = DataLoader(
        val_dataset,
        batch_size=config["training"]["batch_size"],
        shuffle=False
    )
    print("DataLoaders created.")

    # Create trainer
    trainer = PhysicsGuidedTrainer(
        model=model,
        rock_physics_model=rock_physics_model,
        strategy_handler=strategy_handler,
        config=config
    )
    print(f"Trainer initialized on device: {trainer.device}")

    # Setup optimizer and loss
    optimizer = torch.optim.Adam(
        model.parameters(),
        lr=config["training"]["learning_rate"]
    )
    loss_fn = strategy_handler.get_loss_function()

    # --- Training loop ---
    print("\nStarting training...")
    best_val_loss = float('inf')
    patience_counter = 0

    for epoch in range(config["training"]["epochs"]):
        # Train
        train_loss = trainer.train_epoch(train_loader, optimizer, loss_fn)

        # Validate
        val_metrics = trainer.evaluate(val_loader, torch.nn.MSELoss()) # Use simple MSE for eval

        print(f"Epoch {epoch+1}/{config['training']['epochs']} | "
              f"Train Loss: {train_loss:.4f} | "
              f"Val Loss: {val_metrics['loss']:.4f} | "
              f"Val RMSE: {val_metrics['rmse']:.4f} | "
              f"Val Corr: {val_metrics['correlation']:.4f}")

        # Early stopping
        if val_metrics['loss'] < best_val_loss:
            best_val_loss = val_metrics['loss']
            patience_counter = 0
            # Save best model
            # torch.save(model.state_dict(), "best_model.pth")
        else:
            patience_counter += 1
            if patience_counter >= config["training"]["early_stopping_patience"]:
                print("Early stopping triggered.")
                break

    print("\nTraining completed!")

if __name__ == "__main__":
    main()
