import pytest
import yaml
import numpy as np
import torch
from torch.utils.data import DataLoader, TensorDataset

from src.models.neural_networks import BiGRU
from src.models.rock_physics import RockPhysicsFactory
from src.training.strategies import PhysicsGuidanceStrategy, StrategyHandler
from src.training.trainer import PhysicsGuidedTrainer
from src.data.preprocessing import WellLogPreprocessor


def test_rmse_under_100_ms_on_synthetic():
    """Train on a reproducible synthetic dataset and assert RMSE < 100 m/s.

    Dataset: S-wave generated from Mudrock line with small noise.
    Ensures corrected unit pipeline and normalization lead to realistic errors.
    """
    rng = np.random.default_rng(42)

    # Minimal config mirroring defaults
    config = {
        "model": {
            "params": {
                "input_dim": 4,
                "hidden_dim": 16,
                "output_dim": 1,
                "num_layers": 1,
                "dropout": 0.1,
            }
        },
        "rock_physics": {
            "model_type": "mudrock_line",
            "params": {"a": 1.16, "b": 1.36},
        },
        "training": {
            "strategy": "loss_function",
            "batch_size": 16,
            "learning_rate": 0.01,
            "epochs": 25,
        },
        "data": {
            "features": ["P-WAVE", "RHOB", "PHIE", "RT"],
            "target": ["S-WAVE"],
            "vp_feature_name": "P-WAVE",
            "normalization": "minmax",
        },
        "physics_coupling": {
            "vp_units": "m/s",
            "physics_units": "km/s",
            "conversion_factor": 0.001,
        },
    }

    # Create synthetic data in physical units
    n_samples, seq_len = 80, 30
    # P-WAVE in m/s
    p_wave = rng.uniform(2000.0, 6000.0, size=(n_samples, seq_len))
    rhob = rng.uniform(1.8, 2.8, size=(n_samples, seq_len))
    phie = rng.uniform(0.05, 0.35, size=(n_samples, seq_len))
    rt = rng.lognormal(mean=1.0, sigma=1.5, size=(n_samples, seq_len))

    # Ground truth S-wave from Mudrock line (with modest noise)
    s_wave_kms = (p_wave / 1000.0 - 1.36) / 1.16
    s_wave_kms += rng.normal(0.0, 0.03, size=s_wave_kms.shape)  # small noise
    s_wave = np.clip(s_wave_kms, 0.5, 4.0) * 1000.0  # back to m/s

    features = np.stack([p_wave, rhob, phie, rt], axis=-1).astype(np.float32)
    targets = s_wave[..., np.newaxis].astype(np.float32)

    # Preprocess (normalize) with feature engineering enabled
    preprocessor = WellLogPreprocessor(
        normalization=config["data"]["normalization"], feature_engineering=True
    )
    n_base_feats = len(config["data"]["features"])
    features_norm, targets_norm = preprocessor.fit_transform(
        features.reshape(-1, n_base_feats),
        targets.reshape(-1, 1),
        feature_names=config["data"]["features"],
        target_names=config["data"]["target"],
    )

    features_norm = features_norm.reshape(n_samples, seq_len, -1)
    targets_norm = targets_norm.reshape(n_samples, seq_len, 1)

    # Update input_dim post feature engineering
    config["model"]["params"]["input_dim"] = features_norm.shape[-1]

    # Train/val split
    train_size = int(0.8 * n_samples)
    X_train = torch.FloatTensor(features_norm[:train_size])
    y_train = torch.FloatTensor(targets_norm[:train_size])
    X_val = torch.FloatTensor(features_norm[train_size:])
    y_val = torch.FloatTensor(targets_norm[train_size:])

    # Build models and trainer
    rock_physics_model = RockPhysicsFactory.create(
        config["rock_physics"]["model_type"], **config["rock_physics"]["params"]
    )
    strategy = PhysicsGuidanceStrategy(config["training"]["strategy"])
    strategy_handler = StrategyHandler(strategy, rock_physics_model)
    model = BiGRU(**config["model"]["params"])

    trainer = PhysicsGuidedTrainer(
        model=model,
        rock_physics_model=rock_physics_model,
        strategy_handler=strategy_handler,
        config=config,
        preprocessor=preprocessor,
    )

    train_loader = DataLoader(TensorDataset(X_train, y_train), batch_size=config["training"]["batch_size"], shuffle=True)
    val_loader = DataLoader(TensorDataset(X_val, y_val), batch_size=config["training"]["batch_size"], shuffle=False)

    optimizer = torch.optim.Adam(model.parameters(), lr=config["training"]["learning_rate"])
    loss_fn = strategy_handler.get_loss_function()

    # Train for a modest number of epochs
    for _ in range(config["training"]["epochs"]):
        trainer.train_epoch(train_loader, optimizer, loss_fn)

    # Evaluate on val and compute RMSE in physical m/s
    model.eval()
    with torch.no_grad():
        preds_norm = []
        targs_norm = []
        for xb, yb in val_loader:
            preds_norm.append(model(xb.to(trainer.device)).cpu().numpy())
            targs_norm.append(yb.cpu().numpy())
        preds_norm = np.concatenate(preds_norm, axis=0)
        targs_norm = np.concatenate(targs_norm, axis=0)

    # Denormalize to m/s
    preds_phys = preprocessor.inverse_transform_targets(preds_norm)
    targs_phys = preprocessor.inverse_transform_targets(targs_norm)

    rmse_ms = float(np.sqrt(np.mean((preds_phys - targs_phys) ** 2)))
    assert rmse_ms < 100.0, f"RMSE too high: {rmse_ms:.2f} m/s (expected < 100 m/s)"
