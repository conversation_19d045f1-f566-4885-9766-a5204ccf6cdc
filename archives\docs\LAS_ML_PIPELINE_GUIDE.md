# LAS ML Pipeline Test Guide

This guide explains how to use the comprehensive test file that loads LAS data and trains the ML architecture in the codebase.

## Overview

The `test_las_ml_pipeline.py` script provides a complete end-to-end test of the physics-guided machine learning pipeline using real LAS (Log ASCII Standard) files. It demonstrates:

1. **LAS File Loading**: Parse and extract well log curves from LAS files
2. **Data Processing**: Clean, normalize, and create sequences from well log data
3. **ML Training**: Train the BiGRU model with physics guidance
4. **Evaluation**: Assess model performance with comprehensive metrics
5. **Visualization**: Generate plots showing results and comparisons

## Quick Start

### 1. Run the Demo
```bash
python run_las_demo.py
```

### 2. Run the Full Pipeline Test
```bash
python test_las_ml_pipeline.py
```

## What the Test Does

### Phase 1: Data Loading and Inspection
- Loads all LAS files from the `Las/` directory
- Parses well information, curve metadata, and data
- Reports data shapes, available curves, and depth ranges
- Identifies missing or problematic data

### Phase 2: Data Processing
- Maps curve names to standardized names (handles different naming conventions)
- Extracts feature curves: P-<PERSON>VE, RHOB, PHIE, RT
- Extracts target curves: S-WAVE
- Combines data from multiple wells
- Removes invalid/missing data points

### Phase 3: Sequence Creation and Preprocessing
- Creates sequences of length 50 (configurable) for RNN training
- Applies normalization (MinMax scaling by default)
- Adds engineered features (moving averages, ratios)
- Splits data into train/validation/test sets

### Phase 4: Model Setup and Training
- Creates BiGRU neural network based on configuration
- Sets up physics-guided training with mudrock line model
- Implements early stopping and validation monitoring
- Saves best model during training

### Phase 5: Evaluation and Analysis
- Tests model on held-out test set
- Computes comprehensive metrics (RMSE, MAE, R², correlation)
- Compares with physics-only predictions
- Generates detailed performance analysis

### Phase 6: Visualization
- Predictions vs actual scatter plot
- Residual analysis
- Training history curves
- Sample sequence predictions
- Saves all plots to `las_ml_pipeline_results.png`

## Configuration

The test uses `configs/default_config.yaml` or creates a sensible default configuration. Key parameters:

```yaml
model:
  type: "BiGRU"
  params:
    input_dim: 4  # Automatically updated based on features
    hidden_dim: 16
    num_layers: 1
    dropout: 0.1

training:
  strategy: "pseudolabels"  # Physics guidance strategy
  batch_size: 32
  learning_rate: 0.001
  epochs: 50
  early_stopping_patience: 10

data:
  features: ["P-WAVE", "RHOB", "PHIE", "RT"]
  target: ["S-WAVE"]
  sequence_length: 50
  sequence_stride: 5
  normalization: "minmax"
```

## Expected Outputs

### Files Generated
- `las_ml_pipeline_results.png`: Comprehensive visualization plots
- `las_ml_pipeline_results.json`: Detailed numerical results
- `best_las_model.pth`: Trained PyTorch model

### Console Output Example
```
========================================
STARTING COMPLETE LAS TO ML PIPELINE TEST
========================================

1. Loading and inspecting LAS data...
  Loading B-G-10_RP_INPUT.las...
    Shape: (8758, 51)
    Columns: 51
    Depth range: (150.0, 4529.0)

2. Processing and preparing data...
  Successfully processed: B-G-10_RP_INPUT.las
  Successfully processed: B-G-6_RP_INPUT.las
  Successfully processed: B-L-15_RP_INPUT.las
  Combined features shape: (23450, 4)
  Combined targets shape: (23450, 1)

3. Creating sequences and preprocessing...
  Created 4686 sequences
  After preprocessing - Features shape: (4686, 50, 6)

4. Splitting data...
  Train set: 3280 samples
  Val set: 703 samples
  Test set: 703 samples

5. Setting up and training model...
  Model parameters: 1297
  Training device: cpu
  Physics guidance strategy: pseudolabels

6. Training...
    Epoch   1/50 | Train Loss: 0.0234 | Val Loss: 0.0198 | Val RMSE: 0.1407 | Val Corr: 0.8234
    Early stopping at epoch 23

7. Evaluating model...
  Test Results:
    RMSE: 0.1285
    MAE: 0.0987
    R²: 0.8756
    Correlation: 0.9356

8. Testing physics guidance...
  Physics Model (Mudrock Line) Results:
    Equation: VP = 1.160 × VS + 1.360
    RMSE: 0.2341
    Correlation: 0.7234
```

## Understanding the Results

### Key Metrics
- **RMSE**: Root Mean Square Error (lower is better)
- **R²**: Coefficient of determination (higher is better, max 1.0)
- **Correlation**: Pearson correlation coefficient (-1 to 1, closer to 1 is better)

### Physics Guidance Benefits
The test compares ML predictions with physics-only predictions from the mudrock line model. Expected benefits:
- Lower RMSE than physics-only approach
- Better correlation with actual measurements
- More robust predictions in complex geological conditions

### Typical Performance Ranges
- **Good Performance**: R² > 0.8, Correlation > 0.9, RMSE < 0.15 (normalized)
- **Acceptable Performance**: R² > 0.6, Correlation > 0.8, RMSE < 0.25 (normalized)
- **Needs Improvement**: R² < 0.6, Correlation < 0.7, RMSE > 0.3 (normalized)

## Troubleshooting

### Common Issues

1. **No LAS files found**
   - Ensure LAS files are in the `Las/` directory
   - Check file extensions are `.las`

2. **Missing curves**
   - The test automatically maps common curve names
   - Check LAS files have required curves: P-WAVE/VP, S-WAVE/VS, RHOB, PHIE, RT

3. **Memory issues**
   - Reduce `sequence_length` or `batch_size` in config
   - Process fewer wells at once

4. **Poor performance**
   - Check data quality (missing values, outliers)
   - Adjust model parameters (hidden_dim, learning_rate)
   - Try different physics guidance strategies

### Debugging Mode

For detailed debugging, modify the test script:

```python
# Add at the beginning of main()
import logging
logging.basicConfig(level=logging.DEBUG)

# Enable more verbose output
pipeline_test = LASMLPipelineTest(config_path="configs/debug_config.yaml")
```

## Customization

### Adding New Features
To include additional well log curves:

1. Update `data.features` in config
2. Ensure curves exist in LAS files
3. Update curve mapping in `WellLogDataProcessor`

### Different Physics Models
To test other rock physics models:

1. Implement new model in `src/models/rock_physics/`
2. Register in `RockPhysicsFactory`
3. Update `rock_physics.model_type` in config

### Custom Evaluation Metrics
Add metrics in the `_evaluate_model` method:

```python
metrics['custom_metric'] = custom_calculation(predictions_clean, targets_clean)
```

## Integration with Existing Code

The test demonstrates how to integrate LAS loading with the existing ML architecture:

```python
from src.data import LASLoader, WellLogDataProcessor, WellLogPreprocessor
from src.models.neural_networks import BiGRU
from src.models.rock_physics import RockPhysicsFactory
from src.training.trainer import PhysicsGuidedTrainer

# Load LAS data
processor = WellLogDataProcessor()
features, targets, files = processor.process_multiple_wells(las_files)

# Preprocess
preprocessor = WellLogPreprocessor()
features_norm, targets_norm = preprocessor.fit_transform(features, targets)

# Train model
model = BiGRU(input_dim=features_norm.shape[-1])
trainer = PhysicsGuidedTrainer(model, rock_physics_model, strategy_handler)
# ... training loop
```

This pipeline provides a complete reference implementation for using the codebase with real well log data.