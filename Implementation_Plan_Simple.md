# Simplified Physics-Guided BiGRU Implementation Plan

## Executive Summary

This simplified plan addresses the critical gaps identified in the Implementation Analysis with a focus on **practical implementation**, **minimal configuration complexity**, and **immediate impact**. The plan prioritizes core functionality over comprehensive features and uses the existing codebase patterns.

**Current Status**: 84% alignment  
**Target Status**: 92-95% alignment (realistic target)  
**Estimated Timeline**: 3-4 weeks  
**Priority**: Fix critical issues first, then add essential missing components

## Configuration Management Analysis

### 1. YAML vs Alternatives Assessment

**Current State**: The codebase already uses YAML configuration effectively
- ✅ **Existing YAML infrastructure** in `configs/default_config.yaml`
- ✅ **Simple structure** that works well for current needs
- ✅ **Already integrated** in training pipeline

**Recommendation**: **Keep YAML** for the following reasons:
1. **Consistency**: Already established in codebase
2. **Human-readable**: Easy to modify parameters
3. **Hierarchical**: Natural for nested model/training/data configs
4. **Comments**: Supports inline documentation
5. **No migration cost**: Changing would require extensive refactoring

**Alternative Considered**: Python dictionaries
- ❌ **Pros**: No parsing overhead, native Python
- ❌ **Cons**: Less readable, no comments, harder to version control

**Decision**: Enhance existing YAML with minimal additions, not replacement

### 2. Simplified Configuration Enhancement

Instead of complex feature metadata, use **simple parameter additions**:

```yaml
# configs/default_config.yaml - MINIMAL ADDITIONS
data:
  features: ["P-WAVE", "RHOB", "PHIE", "RT"]
  target: ["S-WAVE"]
  vp_feature_name: "P-WAVE"  # Simple VP identification
  apply_log_transform: ["RT"]  # Simple list of features to log-transform

physics_coupling:
  vp_units: "m/s"  # Current VP units in data
  physics_units: "km/s"  # Units expected by physics models
  conversion_factor: 0.001  # Simple conversion factor
```

## Phase 1: Critical Fixes (Week 1-2) - ESSENTIAL

### 1.1 Fix Hard-Coded VP Index (HIGHEST PRIORITY)

**Problem**: Line 46 in `src/training/trainer.py` uses hard-coded index
**Impact**: +2% alignment score
**Timeline**: 1-2 days

#### Simple Solution - No New Classes Needed
**File**: `src/training/trainer.py`

```python
# Current problematic code (line 46):
vp = features[:, :, 2]  # Hard-coded index - PROBLEMATIC

# Simple fix - use config to identify VP feature
def __init__(self, model, rock_physics_model, strategy_handler, config, device="cuda"):
    # ... existing code ...
    self.config = config
    
    # Get VP feature index from config
    feature_names = config["data"]["features"]
    vp_feature_name = config["data"].get("vp_feature_name", "P-WAVE")
    self.vp_index = feature_names.index(vp_feature_name)

# Updated train_epoch method:
def train_epoch(self, dataloader, optimizer, loss_fn):
    # ... existing code ...
    
    # Use config-driven VP index instead of hard-coded
    vp = features[:, :, self.vp_index]
    
    # Simple unit conversion (m/s to km/s)
    conversion_factor = self.config["physics_coupling"].get("conversion_factor", 0.001)
    vp_physics = vp * conversion_factor
    
    # Calculate physics predictions
    physics_pred = self.rock_physics_model.predict(vp_physics.cpu().numpy())
```

**Testing**: 
```python
# Simple test to verify fix
python -c "
import yaml
with open('configs/default_config.yaml') as f:
    config = yaml.safe_load(f)
features = config['data']['features']
vp_idx = features.index(config['data']['vp_feature_name'])
print(f'VP index: {vp_idx}')
"
```

### 1.2 Add RES Log Transform (MEDIUM PRIORITY)

**Impact**: +1.3% alignment score
**Timeline**: 1 day

#### Simple Implementation
**File**: `src/data/preprocessing.py`

```python
# Add to existing WellLogPreprocessor class
def apply_log_transforms(self, data: pd.DataFrame, config: dict) -> pd.DataFrame:
    """Apply log10 transform to specified features."""
    log_features = config["data"].get("apply_log_transform", [])
    
    for feature in log_features:
        if feature in data.columns:
            # Handle zero/negative values by clipping
            data[feature] = np.log10(data[feature].clip(lower=1e-6))
            print(f"Applied log10 transform to {feature}")
    
    return data
```

**Integration**: Update existing training pipeline
```python
# In examples/train_model.py, after data loading:
if "apply_log_transform" in config["data"]:
    data = preprocessor.apply_log_transforms(data, config)
```

## Phase 2: Essential Missing Components (Week 3-4) - HIGH PRIORITY

### 2.1 Pluggable Rock Physics Architecture

**Current State**: Good foundation with factory pattern already exists
**Enhancement**: Make it truly pluggable with minimal code changes

#### Design Pattern: Function-Based Plugins (Simplest Approach)

**File**: `src/models/rock_physics/plugins.py` (NEW)
```python
"""
Simple function-based rock physics plugins.
Each plugin is just a function that can be easily added.
"""
import numpy as np
from sklearn.linear_model import LinearRegression

def empirical_vp_vs_plugin(vp: np.ndarray, coefficients: dict = None) -> np.ndarray:
    """
    Empirical VP-VS relationship: VS = a * VP + b
    Can be fitted per-well or use default coefficients.
    """
    if coefficients is None:
        # Default coefficients (can be overridden)
        a, b = 0.6, 0.1
    else:
        a, b = coefficients['a'], coefficients['b']
    
    return a * vp + b

def multiparameter_plugin(features: np.ndarray, coefficients: dict = None) -> np.ndarray:
    """
    Multiparameter regression: VS = a*GR + b*DEN + c*VP + d*RES + e
    features: [N, 4] array with [GR, DEN, VP, RES]
    """
    if coefficients is None:
        # Default coefficients (can be fitted from data)
        a, b, c, d, e = 0.01, 0.5, 0.6, 0.02, 0.1
    else:
        a, b, c, d, e = coefficients['a'], coefficients['b'], coefficients['c'], coefficients['d'], coefficients['e']
    
    gr, den, vp, res = features[:, 0], features[:, 1], features[:, 2], features[:, 3]
    return a * gr + b * den + c * vp + d * res + e

def fit_empirical_coefficients(vp_data: np.ndarray, vs_data: np.ndarray) -> dict:
    """Fit empirical VP-VS coefficients from training data."""
    model = LinearRegression()
    model.fit(vp_data.reshape(-1, 1), vs_data)
    return {'a': model.coef_[0], 'b': model.intercept_}

def fit_multiparameter_coefficients(features: np.ndarray, vs_data: np.ndarray) -> dict:
    """Fit multiparameter coefficients from training data."""
    model = LinearRegression()
    model.fit(features, vs_data)
    coeffs = model.coef_
    return {'a': coeffs[0], 'b': coeffs[1], 'c': coeffs[2], 'd': coeffs[3], 'e': model.intercept_}

# Plugin registry - simple dictionary
ROCK_PHYSICS_PLUGINS = {
    'empirical_vp_vs': empirical_vp_vs_plugin,
    'multiparameter': multiparameter_plugin,
}

FITTING_FUNCTIONS = {
    'empirical_vp_vs': fit_empirical_coefficients,
    'multiparameter': fit_multiparameter_coefficients,
}
```

#### Integration with Existing Factory (Minimal Changes)

**File**: `src/models/rock_physics/__init__.py`
```python
from .base import RockPhysicsModel
from .mudrock_line import MudrockLine
from .plugins import ROCK_PHYSICS_PLUGINS, FITTING_FUNCTIONS  # NEW

class PluginRockPhysicsModel(RockPhysicsModel):
    """Wrapper to make function-based plugins compatible with existing interface."""
    
    def __init__(self, plugin_name: str, coefficients: dict = None, **kwargs):
        super().__init__(name=plugin_name, **kwargs)
        self.plugin_name = plugin_name
        self.plugin_func = ROCK_PHYSICS_PLUGINS[plugin_name]
        self.coefficients = coefficients
        self._fitted = coefficients is not None
    
    def predict(self, vp: np.ndarray, **kwargs) -> np.ndarray:
        if self.plugin_name == 'multiparameter':
            # Multiparameter needs full feature array
            features = kwargs.get('features')
            if features is None:
                raise ValueError("Multiparameter plugin requires 'features' argument")
            return self.plugin_func(features, self.coefficients)
        else:
            return self.plugin_func(vp, self.coefficients)
    
    def fit(self, vp: np.ndarray, vs: np.ndarray, **kwargs) -> None:
        fitting_func = FITTING_FUNCTIONS.get(self.plugin_name)
        if fitting_func:
            if self.plugin_name == 'multiparameter':
                features = kwargs.get('features')
                if features is None:
                    raise ValueError("Multiparameter fitting requires 'features' argument")
                self.coefficients = fitting_func(features, vs)
            else:
                self.coefficients = fitting_func(vp, vs)
            self._fitted = True

class RockPhysicsFactory:
    """Enhanced factory with plugin support."""
    
    _models = {
        "mudrock_line": MudrockLine,
    }
    
    @classmethod
    def create(cls, model_type: str, **kwargs) -> RockPhysicsModel:
        # Check if it's a traditional model
        if model_type in cls._models:
            return cls._models[model_type](**kwargs)
        
        # Check if it's a plugin
        elif model_type in ROCK_PHYSICS_PLUGINS:
            return PluginRockPhysicsModel(model_type, **kwargs)
        
        else:
            raise ValueError(f"Unknown model type: {model_type}")
    
    @classmethod
    def register_plugin(cls, name: str, plugin_func, fitting_func=None):
        """Register a new plugin function."""
        ROCK_PHYSICS_PLUGINS[name] = plugin_func
        if fitting_func:
            FITTING_FUNCTIONS[name] = fitting_func
```

#### Usage Example
```python
# In config file - no changes needed to existing structure
rock_physics:
  model_type: "empirical_vp_vs"  # or "multiparameter"
  params:
    coefficients:
      a: 0.6
      b: 0.1

# In code - existing factory pattern works
rock_physics_model = RockPhysicsFactory.create(
    config["rock_physics"]["model_type"],
    **config["rock_physics"]["params"]
)
```

### 2.2 Simplified Pseudolabel Integration

**Current Issue**: Only works for 2D arrays, not [B,T,F] sequences
**Impact**: +3% alignment score
**Timeline**: 2-3 days

#### Simple Fix - Minimal Code Changes
**File**: `src/training/strategies.py`

```python
# Update existing prepare_features method
def prepare_features(self, features: np.ndarray, vp_index: int = 2) -> np.ndarray:
    """Enhanced to handle both 2D and 3D arrays."""
    if self.strategy == PhysicsGuidanceStrategy.PSEUDOLABELS:
        original_shape = features.shape
        
        # Handle 3D sequences by reshaping temporarily
        if len(original_shape) == 3:  # [B, T, F]
            batch_size, seq_len, n_features = original_shape
            features_2d = features.reshape(-1, n_features)  # [B*T, F]
        else:  # [B, F]
            features_2d = features
        
        # Extract VP and predict VS using physics model
        vp = features_2d[:, vp_index]
        vs_physics = self.rock_physics_model.predict(vp)
        
        # Add physics predictions as additional feature
        features_augmented = np.column_stack([features_2d, vs_physics])
        
        # Reshape back to original dimensions if needed
        if len(original_shape) == 3:
            new_n_features = features_augmented.shape[1]
            features_augmented = features_augmented.reshape(batch_size, seq_len, new_n_features)
        
        return features_augmented
    
    return features
```

#### Update Model Input Dimension Automatically
**File**: `examples/train_model.py`

```python
# After loading config, before creating model
base_input_dim = config["model"]["params"]["input_dim"]

# Check if pseudolabels strategy adds extra dimension
if config["training"]["strategy"] == "pseudolabels":
    actual_input_dim = base_input_dim + 1  # +1 for pseudolabel channel
else:
    actual_input_dim = base_input_dim

# Update config for model creation
config["model"]["params"]["input_dim"] = actual_input_dim

# Create model with correct input dimension
model = BiGRU(**config["model"]["params"])
```

## Phase 3: Evaluation Enhancement (Week 4) - MEDIUM PRIORITY

### 3.1 Simplified Evaluation Framework

**Objective**: Basic cross-well evaluation without over-engineering
**Impact**: +2% alignment score
**Timeline**: 2-3 days

#### Minimal Evaluation Class
**File**: `src/evaluation/simple_evaluator.py` (NEW)
```python
"""
Simplified evaluation framework focusing on essential metrics.
"""
import numpy as np
from sklearn.metrics import mean_squared_error, mean_absolute_error
from scipy.stats import pearsonr

class SimpleEvaluator:
    """Simple cross-well evaluator without complex orchestration."""
    
    def __init__(self, config):
        self.config = config
        self.conversion_factor = config["physics_coupling"].get("conversion_factor", 0.001)
    
    def evaluate_model(self, model, test_features, test_targets):
        """Evaluate model on test data with physical unit conversion."""
        with torch.no_grad():
            predictions = model(test_features)
            predictions = predictions.cpu().numpy()
        
        targets = test_targets.cpu().numpy()
        
        # Convert to physical units (km/s) for evaluation
        predictions_physical = predictions / self.conversion_factor  # Convert back to km/s
        targets_physical = targets / self.conversion_factor
        
        # Calculate metrics
        rmse = np.sqrt(mean_squared_error(targets_physical, predictions_physical))
        mae = mean_absolute_error(targets_physical, predictions_physical)
        correlation, _ = pearsonr(targets_physical.flatten(), predictions_physical.flatten())
        
        return {
            'rmse_km_s': rmse,
            'mae_km_s': mae,
            'correlation': correlation,
            'n_samples': len(targets_physical)
        }
    
    def cross_well_evaluation(self, wells_data, model_class, config):
        """Simple cross-well evaluation - train on each well, test on others."""
        results = {}
        well_names = list(wells_data.keys())
        
        for train_well in well_names:
            print(f"Training on: {train_well}")
            
            # Simple train/test split
            train_data = wells_data[train_well]
            test_wells = {w: wells_data[w] for w in well_names if w != train_well}
            
            # Train model (simplified - use existing training pipeline)
            model = self._train_simple_model(train_data, model_class, config)
            
            # Evaluate on other wells
            well_results = {}
            for test_well, test_data in test_wells.items():
                metrics = self.evaluate_model(model, test_data[0], test_data[1])
                well_results[test_well] = metrics
            
            results[train_well] = well_results
        
        return results
    
    def _train_simple_model(self, train_data, model_class, config):
        """Simplified model training - reuse existing pipeline."""
        # This would use the existing training pipeline
        # Implementation depends on data format
        pass
```

## Implementation Timeline & Priorities

### Week 1: Critical Fixes
- [ ] **Day 1-2**: Fix hard-coded VP index in trainer
- [ ] **Day 3**: Add RES log transform
- [ ] **Day 4-5**: Testing and validation

### Week 2: Rock Physics Plugins
- [ ] **Day 6-8**: Implement function-based plugin system
- [ ] **Day 9-10**: Add empirical VP-VS and multiparameter plugins
- [ ] **Day 11-12**: Integration testing

### Week 3: Pseudolabel Enhancement
- [ ] **Day 13-15**: Fix sequence dimension handling in pseudolabels
- [ ] **Day 16-17**: Update model input dimension logic
- [ ] **Day 18-19**: End-to-end testing

### Week 4: Evaluation & Polish
- [ ] **Day 20-22**: Simple evaluation framework
- [ ] **Day 23-24**: Documentation and final testing
- [ ] **Day 25**: Performance validation

## Feasibility Assessment

### Essential (Must Implement)
1. **Fix hard-coded VP index** - Critical for physics coupling
2. **RES log transform** - Simple but impactful
3. **Function-based rock physics plugins** - Addresses major gap with minimal complexity

### Simplified (Reduced Scope)
1. **Pseudolabel sequence handling** - Fix existing limitation without over-engineering
2. **Basic evaluation framework** - Essential metrics without complex orchestration

### Deferred (Future Iterations)
1. **Transfer learning** - Complex, can be added later
2. **Comprehensive experiment orchestration** - Over-engineered for current needs
3. **Complex configuration validation** - Current YAML works fine

## Expected Outcomes

### Realistic Targets
- **Current**: 84% alignment
- **After Phase 1**: 87% (+3% from critical fixes)
- **After Phase 2**: 92% (+5% from missing components)
- **After Phase 3**: 94% (+2% from evaluation improvements)

### Key Benefits
1. **Immediate Impact**: Critical fixes provide quick wins
2. **Maintainable**: Uses existing patterns and minimal new complexity
3. **Extensible**: Plugin system allows easy addition of new physics models
4. **Practical**: Focuses on working solutions over perfect architecture

This simplified plan prioritizes **working solutions** over **perfect architecture**, ensuring rapid progress while maintaining code quality and extensibility.
