import pytest
import torch
import numpy as np
from torch.utils.data import <PERSON><PERSON><PERSON><PERSON>, TensorDataset

from src.models.neural_networks import BiGRU
from src.models.rock_physics import RockPhysicsFactory
from src.training.strategies import PhysicsGuidanceStrategy, StrategyHandler
from src.training.trainer import PhysicsGuidedTrainer
from src.training.losses import PhysicsGuidedLoss

@pytest.fixture
def synthetic_data():
    """Create synthetic data for testing."""
    n_samples = 100
    seq_len = 20
    n_features = 5
    features = np.random.rand(n_samples, seq_len, n_features).astype(np.float32)
    targets = np.random.rand(n_samples, seq_len, 1).astype(np.float32)
    # Ensure VP is in a reasonable range (e.g., 2 to 5 km/s)
    features[:, :, 2] = 2 + 3 * np.random.rand(n_samples, seq_len)
    return torch.from_numpy(features), torch.from_numpy(targets)

@pytest.fixture
def config():
    """Provide a mock configuration."""
    return {
        "model": {
            "input_dim": 5,
            "hidden_dim": 8,
            "output_dim": 1,
            "num_layers": 1,
            "dropout": 0.1,
        },
        "rock_physics": {
            "model_type": "mudrock_line",
            "params": {"a": 1.16, "b": 1.36},
        },
    }

def test_trainer_initialization(config):
    """Test the initialization of the PhysicsGuidedTrainer."""
    model = BiGRU(**config["model"])
    rock_physics_model = RockPhysicsFactory.create(
        config["rock_physics"]["model_type"], **config["rock_physics"]["params"]
    )
    strategy_handler = StrategyHandler(
        PhysicsGuidanceStrategy.LOSS_FUNCTION, rock_physics_model
    )
    trainer = PhysicsGuidedTrainer(model, rock_physics_model, strategy_handler, config)
    assert isinstance(trainer, PhysicsGuidedTrainer)
    assert trainer.device in ["cuda", "cpu"]

def test_training_epoch_with_loss_function_strategy(synthetic_data, config):
    """Test a single training epoch using the loss function strategy."""
    features, targets = synthetic_data
    dataset = TensorDataset(features, targets)
    dataloader = DataLoader(dataset, batch_size=10)

    model = BiGRU(**config["model"])
    rock_physics_model = RockPhysicsFactory.create(
        config["rock_physics"]["model_type"], **config["rock_physics"]["params"]
    )
    strategy_handler = StrategyHandler(
        PhysicsGuidanceStrategy.LOSS_FUNCTION, rock_physics_model
    )
    trainer = PhysicsGuidedTrainer(model, rock_physics_model, strategy_handler, config)

    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    loss_fn = strategy_handler.get_loss_function()

    # Ensure the loss function is correct
    assert isinstance(loss_fn, PhysicsGuidedLoss)

    # Run a training epoch
    train_loss = trainer.train_epoch(dataloader, optimizer, loss_fn)

    assert isinstance(train_loss, float)
    assert train_loss > 0

def test_evaluation_step(synthetic_data, config):
    """Test the evaluation method of the trainer."""
    features, targets = synthetic_data
    dataset = TensorDataset(features, targets)
    dataloader = DataLoader(dataset, batch_size=10)

    model = BiGRU(**config["model"])
    rock_physics_model = RockPhysicsFactory.create(
        config["rock_physics"]["model_type"], **config["rock_physics"]["params"]
    )
    strategy_handler = StrategyHandler(
        PhysicsGuidanceStrategy.LOSS_FUNCTION, rock_physics_model
    )
    trainer = PhysicsGuidedTrainer(model, rock_physics_model, strategy_handler, config)

    # Use a simple loss for evaluation
    loss_fn = torch.nn.MSELoss()

    # Run evaluation
    metrics = trainer.evaluate(dataloader, loss_fn)

    assert "loss" in metrics
    assert "rmse" in metrics
    assert "correlation" in metrics
    assert metrics["loss"] > 0
    assert metrics["rmse"] > 0
    # Correlation can be negative for random data
    assert -1 <= metrics["correlation"] <= 1
