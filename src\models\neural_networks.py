import torch
import torch.nn as nn
from typing import Optional, List

class BiGRU(nn.Module):
    """
    Bidirectional GRU network for sequence modeling.
    Based on <PERSON> et al. (2024) architecture.
    """

    def __init__(
        self,
        input_dim: int,
        hidden_dim: int = 16,
        output_dim: int = 1,
        num_layers: int = 1,
        dropout: float = 0.1
    ):
        super().__init__()

        self.hidden_dim = hidden_dim
        self.num_layers = num_layers

        self.gru = nn.GRU(
            input_dim,
            hidden_dim,
            num_layers,
            batch_first=True,
            bidirectional=True,
            dropout=dropout if num_layers > 1 else 0
        )

        # Output layer (bidirectional so hidden_dim * 2)
        self.fc = nn.Linear(hidden_dim * 2, output_dim)
        self.dropout = nn.Dropout(dropout)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass through the network.

        Args:
            x: Input tensor of shape (batch_size, seq_len, input_dim)

        Returns:
            Output tensor of shape (batch_size, seq_len, output_dim)
        """
        # GRU forward pass
        gru_out, _ = self.gru(x)

        # Apply dropout and final linear layer
        out = self.dropout(gru_out)
        out = self.fc(out)

        return out
