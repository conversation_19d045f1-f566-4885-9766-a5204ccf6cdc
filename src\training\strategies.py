from enum import Enum
from typing import Dict, Any, Optional
import torch
import numpy as np

class PhysicsGuidanceStrategy(Enum):
    """Enumeration of physics guidance strategies."""
    PSEUDOLABELS = "pseudolabels"
    LOSS_FUNCTION = "loss_function"
    TRANSFER_LEARNING = "transfer_learning"

class StrategyHandler:
    """Handler for different physics guidance strategies."""

    def __init__(self, strategy: PhysicsGuidanceStrategy, rock_physics_model):
        self.strategy = strategy
        self.rock_physics_model = rock_physics_model

    def prepare_features(
        self,
        features: np.ndarray,
        vp_index: Optional[int] = None
    ) -> np.ndarray:
        """
        Prepare features based on strategy.

        Args:
            features: Input features array
            vp_index: Index of VP in features

        Returns:
            Modified features array
        """
        if self.strategy == PhysicsGuidanceStrategy.PSEUDOLABELS:
            if vp_index is None:
                raise ValueError("vp_index must be provided to prepare_features; avoid hard-coded defaults.")
            # Extract VP and predict VS using physics model
            vp = features[:, vp_index]
            vs_physics = self.rock_physics_model.predict(vp)

            # Add physics predictions as additional feature
            features = np.column_stack([features, vs_physics])

        return features

    def get_loss_function(self):
        """Get appropriate loss function for strategy."""
        if self.strategy == PhysicsGuidanceStrategy.LOSS_FUNCTION:
            from .losses import PhysicsGuidedLoss
            return PhysicsGuidedLoss()
        else:
            return torch.nn.MSELoss()
