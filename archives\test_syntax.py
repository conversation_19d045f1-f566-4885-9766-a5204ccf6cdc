#!/usr/bin/env python3
"""
Simple syntax test for the modified test_las_ml_pipeline.py
"""

import ast
import sys

def check_syntax(filename):
    """Check if a Python file has valid syntax."""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            source = f.read()
        
        # Parse the source code
        ast.parse(source)
        print(f"✓ Syntax check passed for {filename}")
        return True
        
    except SyntaxError as e:
        print(f"✗ Syntax error in {filename}:")
        print(f"  Line {e.lineno}: {e.text.strip() if e.text else 'N/A'}")
        print(f"  Error: {e.msg}")
        return False
        
    except Exception as e:
        print(f"✗ Error checking {filename}: {e}")
        return False

if __name__ == "__main__":
    filename = "test_las_ml_pipeline.py"
    if check_syntax(filename):
        print("File syntax is valid!")
        sys.exit(0)
    else:
        print("File has syntax errors!")
        sys.exit(1)
