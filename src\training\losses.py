import torch
import torch.nn as nn
from typing import Optional

class PhysicsGuidedLoss(nn.Module):
    """
    Physics-guided loss function combining data loss and physics loss.
    Based on <PERSON> et al. (2024) Equation 10.
    """

    def __init__(self, base_loss: nn.Module = nn.MSELoss()):
        super().__init__()
        self.base_loss = base_loss

    def forward(
        self,
        pred: torch.Tensor,
        target: torch.Tensor,
        physics_pred: Optional[torch.Tensor] = None
    ) -> torch.Tensor:
        """
        Calculate combined loss.

        Args:
            pred: Model predictions
            target: True labels
            physics_pred: Physics model predictions

        Returns:
            Combined loss value
        """
        # Data-driven loss
        loss_data = self.base_loss(pred, target)

        if physics_pred is not None:
            # Physics-based loss
            loss_physics = self.base_loss(pred, physics_pred)

            # Adaptive combination (Equation 10 from paper)
            loss = loss_data + torch.min(loss_data, loss_physics)
        else:
            loss = loss_data

        return loss
