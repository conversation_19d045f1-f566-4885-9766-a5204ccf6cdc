#!/usr/bin/env python3
"""
Test script to verify that the scaling issues are resolved in the full pipeline.
"""
import sys
import os
sys.path.append('.')

from test_las_ml_pipeline import LASMLPipelineTest

def main():
    """Run the pipeline test to verify scaling fix."""
    print("Testing Full Pipeline with Scaling Fix")
    print("=" * 50)
    
    try:
        # Run the complete test
        pipeline = LASMLPipelineTest(enable_interactive=False)
        print(f'Found {len(pipeline.las_files)} LAS files')
        
        if len(pipeline.las_files) == 0:
            print("No LAS files found for testing")
            return False
        
        print('Running complete pipeline test...')
        results = pipeline.run_complete_test()
        
        if 'evaluation' in results:
            eval_results = results['evaluation']
            print(f'\nTest completed successfully!')
            print(f'RMSE: {eval_results.get("rmse", "N/A"):.2f} m/s')
            print(f'R²: {eval_results.get("r2", "N/A"):.3f}')
            print(f'Correlation: {eval_results.get("correlation", "N/A"):.3f}')
            
            # Check if RMSE is reasonable (should be much better than 644 m/s from before)
            rmse = eval_results.get('rmse', float('inf'))
            if rmse < 400:
                print('✅ Scaling appears to be FIXED! RMSE is much improved.')
                return True
            else:
                print('⚠️  RMSE still high, may need more investigation.')
                return False
        else:
            print('Test completed but no evaluation results available')
            return False
            
    except Exception as e:
        print(f'Error: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 Pipeline scaling test PASSED!")
    else:
        print("\n❌ Pipeline scaling test needs attention")
        sys.exit(1)
