# Physics-Guided Machine Learning for Shear Sonic Log Prediction

This repository contains a Python framework for implementing physics-guided machine learning for predicting S-wave velocity (VS) from conventional well logs, based on the work by <PERSON> et al. (2024).

The framework is designed to be modular and extensible, allowing for the easy addition of new rock physics models and training strategies.

## Project Structure

```
Bigru_OPS/
├── src/                          # Main source code directory
│   ├── __init__.py
│   ├── data/                     # Data loading and preprocessing
│   │   ├── __init__.py
│   │   ├── las_loader.py         # LAS file parsing and loading
│   │   └── preprocessing.py      # Data preprocessing utilities
│   ├── models/                   # Neural network and physics models
│   │   ├── __init__.py
│   │   ├── neural_networks.py    # BiGRU and other NN architectures
│   │   └── rock_physics/         # Rock physics models
│   │       ├── __init__.py       # Factory for rock physics models
│   │       ├── base.py           # Abstract base classes
│   │       └── mudrock_line.py   # Mudrock line model implementation
│   ├── training/                 # Training infrastructure
│   │   ├── __init__.py
│   │   ├── trainer.py            # Main training orchestrator
│   │   ├── losses.py             # Physics-guided loss functions
│   │   └── strategies.py         # Physics guidance strategies
│   └── utils/                    # Utility functions
│       └── __init__.py
├── tests/                        # Unit tests
│   ├── test_rock_physics.py      # Rock physics model tests
│   └── test_training.py          # Training pipeline tests
├── configs/                      # Configuration files
│   └── default_config.yaml       # Default training configuration
├── examples/                     # Example scripts and demos
│   └── train_model.py            # Main training example
├── Las/                          # Sample LAS files for testing
│   ├── B-G-10_RP_INPUT.las
│   ├── B-G-6_RP_INPUT.las
│   └── B-L-15_RP_INPUT.las
├── run_las_demo.py               # Quick demo script for LAS processing
├── test_las_ml_pipeline.py       # End-to-end pipeline test
├── requirements.txt              # Python dependencies
├── LICENSE                       # License file
├── Guide_ops41.md                # Detailed implementation guide
├── LAS_ML_PIPELINE_GUIDE.md      # LAS processing pipeline guide
└── README.md                     # This file
```

### Key Components

#### Core Modules (`src/`)
- **`data/`**: Handles LAS file loading and data preprocessing
  - `las_loader.py`: Parses LAS (Log ASCII Standard) files and extracts well log data
  - `preprocessing.py`: Data cleaning, normalization, and sequence preparation

- **`models/`**: Contains neural network architectures and physics models
  - `neural_networks.py`: BiGRU implementation and other deep learning models
  - `rock_physics/`: Physics-based models for S-wave velocity prediction
    - `base.py`: Abstract interfaces for rock physics models
    - `mudrock_line.py`: Mudrock line model (VP-VS relationship)

- **`training/`**: Training infrastructure and strategies
  - `trainer.py`: Main training orchestrator with physics guidance
  - `losses.py`: Physics-informed loss functions
  - `strategies.py`: Different physics guidance strategies (pseudolabels, loss function, transfer learning)

#### Test Files
- **`run_las_demo.py`**: Quick demonstration of LAS file processing
- **`test_las_ml_pipeline.py`**: Comprehensive end-to-end pipeline test
- **`tests/`**: Unit tests for individual components

#### Configuration and Examples
- **`configs/default_config.yaml`**: Default configuration for model, training, and data parameters
- **`examples/train_model.py`**: Complete training example with configuration loading
- **`Las/`**: Sample LAS files for testing and demonstration

## Installation

1.  Clone the repository:
    ```bash
    git clone <repository-url>
    cd physics-guided-ml
    ```

2.  Create and activate a virtual environment:
    ```bash
    python -m venv venv
    source venv/bin/activate  # On Windows: venv\Scripts\activate
    ```

3.  Install the required dependencies:
    ```bash
    pip install -r requirements.txt
    ```

## Usage

### Quick Start with LAS Files

For a quick demonstration using real LAS data:
```bash
python run_las_demo.py
```

### Full Training Pipeline

To run the complete training pipeline with LAS data:
```bash
python test_las_ml_pipeline.py
```

### Training with Custom Configuration

To run training with the configurable example:
```bash
python examples/train_model.py
```

The configuration for the model, training, and data can be modified in `configs/default_config.yaml`.

### Available Entry Points

1. **`run_las_demo.py`**: Quick demo showing LAS file loading and basic processing
2. **`test_las_ml_pipeline.py`**: Complete end-to-end pipeline with real LAS data, training, and evaluation
3. **`examples/train_model.py`**: Configurable training script using synthetic data

## Testing

### Unit Tests
To run the unit test suite:
```bash
pytest tests/
```

### End-to-End Pipeline Test
To run the comprehensive pipeline test with real LAS data:
```bash
python test_las_ml_pipeline.py
```

### Available Tests
- **`tests/test_rock_physics.py`**: Tests for rock physics models
- **`tests/test_training.py`**: Tests for training components
- **`test_las_ml_pipeline.py`**: Complete pipeline integration test

## Data Format

### LAS Files
The framework works with LAS (Log ASCII Standard) files, which are the industry standard for well log data. The sample files in the `Las/` directory demonstrate the expected format:

- **Required curves**: P-WAVE (compressional velocity), RHOB (bulk density), PHIE (porosity), RT (resistivity)
- **Target curve**: S-WAVE (shear velocity) for supervised training
- **Depth information**: DEPT curve for depth indexing

### Curve Name Mapping
The system automatically maps different naming conventions:
- P-WAVE: VP, DTCO, DTC, P-WAVE
- S-WAVE: VS, DTSM, DTS, S-WAVE
- RHOB: RHOB, DEN, DENSITY
- PHIE: PHIE, NPHI, POROSITY
- RT: RT, RES, RESISTIVITY

## Extending the Framework

### Adding a New Rock Physics Model

1.  Create a new file in `src/models/rock_physics/` (e.g., `xu_white.py`).
2.  Create a class that inherits from `RockPhysicsModel` (in `src/models/rock_physics/base.py`).
3.  Implement the required methods: `predict()`, `fit()`, and `get_parameters()`.
4.  Register the new model in the `RockPhysicsFactory` in `src/models/rock_physics/__init__.py`.

### Adding a New Training Strategy

1.  Add a new strategy to the `PhysicsGuidanceStrategy` enum in `src/training/strategies.py`.
2.  Implement the strategy logic in the `StrategyHandler` class.
3.  Update the trainer to handle the new strategy if needed.

### Adding New Neural Network Architectures

1.  Add new model classes to `src/models/neural_networks.py`.
2.  Ensure compatibility with the existing training pipeline.
3.  Update configuration files to support the new architecture.

## References
- Zhao, L., et al. (2024). "Rock-physics-guided machine learning for shear sonic log prediction." Geophysics, 89(1), D75-D87.
- Castagna, J.P., et al. (1985). "Relationships between compressional-wave and shear-wave velocities in clastic silicate rocks." Geophysics, 50(4), 571-581.
