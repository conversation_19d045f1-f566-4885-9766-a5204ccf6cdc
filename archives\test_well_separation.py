#!/usr/bin/env python3
"""
Test script to validate well data separation in the modified test_las_ml_pipeline.py
"""

import os
import sys
import numpy as np
from pathlib import Path

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_well_separation():
    """Test that well data is properly separated."""
    try:
        from test_las_ml_pipeline import LASMLPipelineTest
        
        print("Testing well data separation...")
        
        # Initialize the pipeline test
        pipeline_test = LASMLPipelineTest(enable_interactive=False)
        
        # Check if LAS files are found and sorted
        print(f"Found {len(pipeline_test.las_files)} LAS files:")
        sorted_files = sorted(pipeline_test.las_files, key=lambda x: os.path.basename(x))
        for i, file in enumerate(sorted_files):
            print(f"  {i+1}. {os.path.basename(file)}")
        
        # Test data loading
        print("\nTesting data loading...")
        data_loading_results = pipeline_test._test_data_loading()
        
        print(f"Successfully loaded {len(data_loading_results)} wells:")
        for well_name, info in data_loading_results.items():
            if info.get('usable_for_training', False):
                print(f"  ✓ {well_name}: {info['total_rows']} rows, Quality: {info['data_quality_score']:.1f}%")
            else:
                print(f"  ✗ {well_name}: Not usable - {info.get('error', 'Missing data')}")
        
        # Test individual well data storage
        if hasattr(pipeline_test, 'well_data_individual'):
            print(f"\nIndividual well data storage: {len(pipeline_test.well_data_individual)} wells")
            for well_name, well_data in pipeline_test.well_data_individual.items():
                print(f"  {well_name}: {len(well_data['features'])} features, {len(well_data['targets'])} targets")
        else:
            print("\nWarning: well_data_individual not found - need to run data processing first")
        
        print("\n✓ Well separation test completed successfully!")
        return True
        
    except Exception as e:
        print(f"\n✗ Error in well separation test: {e}")
        import traceback
        traceback.print_exc()
        return False

def validate_plotting_functions():
    """Validate that plotting functions exist and have correct signatures."""
    try:
        from test_las_ml_pipeline import LASMLPipelineTest
        
        print("\nValidating plotting functions...")
        
        pipeline_test = LASMLPipelineTest(enable_interactive=False)
        
        # Check if new methods exist
        required_methods = [
            '_create_well_specific_visualizations',
            '_create_individual_well_plots', 
            '_plot_single_well',
            '_create_wells_comparison_plot'
        ]
        
        for method_name in required_methods:
            if hasattr(pipeline_test, method_name):
                print(f"  ✓ {method_name} - Found")
            else:
                print(f"  ✗ {method_name} - Missing")
                return False
        
        print("✓ All plotting functions validated successfully!")
        return True
        
    except Exception as e:
        print(f"✗ Error validating plotting functions: {e}")
        return False

if __name__ == "__main__":
    print("="*60)
    print("WELL SEPARATION VALIDATION TEST")
    print("="*60)
    
    success = True
    
    # Test 1: Well separation
    success &= test_well_separation()
    
    # Test 2: Plotting functions
    success &= validate_plotting_functions()
    
    print("\n" + "="*60)
    if success:
        print("✓ ALL TESTS PASSED - Well separation is working correctly!")
    else:
        print("✗ SOME TESTS FAILED - Check the output above for details")
    print("="*60)
    
    sys.exit(0 if success else 1)
