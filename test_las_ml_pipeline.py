"""
Comprehensive test file for loading LAS data and training ML models.
This script demonstrates the complete pipeline from LAS file loading to model training and prediction.
"""
import os
import sys
import yaml
import json
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.widgets import RectangleSelector
import matplotlib.patches as patches
import torch
from torch.utils.data import DataLoader, TensorDataset
import pandas as pd
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import seaborn as sns
from tqdm import tqdm
from datetime import datetime
from pathlib import Path

# Add src to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from data.las_loader import LASLoader, WellLogDataProcessor
from data.preprocessing import WellLogPreprocessor, split_data, apply_log_transforms
from models.neural_networks import BiGRU
from models.rock_physics import RockPhysicsFactory
from training.strategies import PhysicsGuidanceStrategy, <PERSON><PERSON><PERSON><PERSON>
from training.trainer import PhysicsGuidedTrainer


class LASMLPipelineTest:
    """
    Test class for the complete LAS to ML pipeline.
    """

    def __init__(self, config_path: str = "configs/default_config.yaml",
                 output_dir: str = "output", enable_interactive: bool = True):
        """
        Initialize the test pipeline.

        Args:
            config_path: Path to configuration file
            output_dir: Directory to save output files
            enable_interactive: Enable interactive matplotlib displays
        """
        self.config_path = config_path
        self.output_dir = Path(output_dir)
        self.enable_interactive = enable_interactive
        self.config = self._load_config()
        self.las_files = self._find_las_files()
        self.results = {}
        self.well_info = {}  # Track well information and metadata
        self.well_usage = {}  # Track well usage in train/val/test splits
        self.well_data_individual = {}  # Store individual well data for plotting

        # Create output directory
        self._setup_output_directory()

    def _setup_output_directory(self):
        """Create output directory and subdirectories if they don't exist."""
        try:
            self.output_dir.mkdir(parents=True, exist_ok=True)

            # Create subdirectories for organization
            (self.output_dir / "visualizations").mkdir(exist_ok=True)
            (self.output_dir / "models").mkdir(exist_ok=True)
            (self.output_dir / "results").mkdir(exist_ok=True)

            print(f"Output directory setup: {self.output_dir.absolute()}")

        except Exception as e:
            print(f"Warning: Could not create output directory {self.output_dir}: {e}")
            print("Files will be saved to current directory")
            self.output_dir = Path(".")

    def _load_config(self) -> dict:
        """Load configuration from YAML file."""
        if not os.path.exists(self.config_path):
            print(f"Config file not found: {self.config_path}")
            return self._create_default_config()

        with open(self.config_path, 'r') as f:
            config = yaml.safe_load(f)

        print(f"Configuration loaded from {self.config_path}")
        return config

    def _create_default_config(self) -> dict:
        """Create default configuration if config file doesn't exist."""
        return {
            'model': {
                'type': 'BiGRU',
                'params': {
                    'input_dim': 4,  # Will be updated based on data
                    'hidden_dim': 16,
                    'output_dim': 1,
                    'num_layers': 1,
                    'dropout': 0.1
                }
            },
            'rock_physics': {
                'model_type': 'mudrock_line',
                'params': {
                    'a': 1.16,
                    'b': 1.36
                }
            },
            'training': {
                'strategy': 'pseudolabels',
                'batch_size': 32,
                'learning_rate': 0.001,
                'epochs': 50,
                'early_stopping_patience': 10
            },
            'data': {
                'features': ['P-WAVE', 'RHOB', 'PHIE', 'RT'],
                'target': ['S-WAVE'],
                'train_test_split': 0.8,
                'normalization': 'minmax',
                'sequence_length': 100,  # Longer sequences for better pattern learning
                'sequence_stride': 10    # Better data utilization with smaller stride
            }
        }

    def _find_las_files(self) -> list:
        """Find all LAS files in the Las directory."""
        las_dir = os.path.join(os.path.dirname(__file__), 'Las')
        if not os.path.exists(las_dir):
            raise FileNotFoundError(f"LAS directory not found: {las_dir}")

        las_files = [
            os.path.join(las_dir, f) for f in os.listdir(las_dir)
            if f.lower().endswith('.las')
        ]

        if not las_files:
            raise FileNotFoundError(f"No LAS files found in {las_dir}")

        print(f"Found {len(las_files)} LAS files:")
        for file in las_files:
            print(f"  - {os.path.basename(file)}")

        return las_files

    def run_complete_test(self) -> dict:
        """
        Run the complete test pipeline.

        Returns:
            Dictionary containing test results
        """
        print("="*60)
        print("STARTING COMPLETE LAS TO ML PIPELINE TEST")
        print("="*60)

        try:
            # Step 1: Load and inspect LAS data
            print("\n1. Loading and inspecting LAS data...")
            self.results['data_loading'] = self._test_data_loading()

            # Step 2: Process and prepare data
            print("\n2. Processing and preparing data...")
            features, targets, successful_files = self._process_data()
            self.results['data_processing'] = {
                'feature_shape': features.shape,
                'target_shape': targets.shape,
                'successful_files': len(successful_files)
            }

            # Step 3: Create sequences and preprocess
            print("\n3. Creating sequences and preprocessing...")
            seq_features, seq_targets, preprocessor = self._prepare_sequences(features, targets)
            self.results['sequence_creation'] = {
                'sequence_features_shape': seq_features.shape,
                'sequence_targets_shape': seq_targets.shape
            }

            # Step 4: Split data
            print("\n4. Splitting data...")
            data_splits = self._split_data(seq_features, seq_targets)
            train_features, val_features, test_features, train_targets, val_targets, test_targets = data_splits

            # Step 5: Setup and train model
            print("\n5. Setting up and training model...")
            model, trainer, loss_fn = self._setup_model(train_features.shape[-1], preprocessor)
            training_history = self._train_model(
                model, trainer, loss_fn, train_features, train_targets, val_features, val_targets
            )
            self.results['training'] = training_history

            # Step 6: Evaluate model
            print("\n6. Evaluating model...")
            evaluation_results = self._evaluate_model(
                model, test_features, test_targets, preprocessor
            )
            self.results['evaluation'] = evaluation_results

            # Step 7: Test physics guidance
            print("\n7. Testing physics guidance...")
            physics_results = self._test_physics_guidance(test_features, test_targets)
            self.results['physics_guidance'] = physics_results

            # Step 8: Visualize results
            print("\n8. Creating comprehensive visualizations...")
            self._create_comprehensive_visualizations(
                data_splits, model, preprocessor, evaluation_results
            )

            # Step 9: Create physics-informed comparison plots
            print("\n9. Creating physics-informed comparison plots...")
            self._create_physics_comparison_plots(
                data_splits, model, preprocessor, evaluation_results, physics_results
            )

            # Step 10: Create well-specific visualizations
            print("\n10. Creating well-specific visualizations...")
            self._create_well_specific_visualizations(
                data_splits, model, preprocessor, evaluation_results
            )

            print("\n" + "="*60)
            print("PIPELINE TEST COMPLETED SUCCESSFULLY!")
            print("="*60)
            self._print_summary()

        except Exception as e:
            print(f"\nERROR in pipeline: {str(e)}")
            import traceback
            traceback.print_exc()
            self.results['error'] = str(e)

        return self.results

    def _test_data_loading(self) -> dict:
        """Test loading individual LAS files and collect comprehensive well information."""
        results = {}
        loader = LASLoader()

        print(f"  Loading and analyzing {len(self.las_files)} LAS files...")

        for i, las_file in enumerate(self.las_files):  # Process all files, not just first 3
            well_name = os.path.basename(las_file)
            try:
                print(f"  Loading {well_name}...")
                data = loader.load(las_file)

                # Calculate comprehensive well information
                well_info = self._analyze_well_data(las_file, data, loader)

                # Store in both results and well_info
                results[well_name] = well_info
                self.well_info[well_name] = well_info

                print(f"    Shape: {data.shape}")
                print(f"    Depth range: {well_info['depth_range'][0]:.1f} - {well_info['depth_range'][1]:.1f}m")
                print(f"    Data quality: {well_info['data_quality_score']:.1f}%")
                print(f"    Required curves available: {well_info['required_curves_available']}/{well_info['total_required_curves']}")

            except Exception as e:
                print(f"    Error: {str(e)}")
                error_info = {
                    'error': str(e),
                    'file_path': las_file,
                    'data_quality_score': 0.0,
                    'usable_for_training': False
                }
                results[well_name] = error_info
                self.well_info[well_name] = error_info

        return results

    def _analyze_well_data(self, las_file: str, data: pd.DataFrame, loader: 'LASLoader') -> dict:
        """Analyze individual well data and extract comprehensive metadata."""
        well_name = os.path.basename(las_file)

        # Basic information
        well_info = {
            'file_path': las_file,
            'well_name': well_name,
            'total_rows': len(data),
            'total_columns': len(data.columns),
            'columns': list(data.columns),
            'depth_range': (data.iloc[0, 0], data.iloc[-1, 0]) if len(data) > 0 else (0, 0),
            'depth_increment': (data.iloc[1, 0] - data.iloc[0, 0]) if len(data) > 1 else 0.5,
        }

        # Check for required curves
        required_features = self.config['data']['features']
        required_targets = self.config['data']['target']
        all_required = required_features + required_targets

        # Get curve mapping from the loader
        available_curves = loader.get_curve_names()
        processor = WellLogDataProcessor()
        curve_mapping = processor._get_curve_mapping(available_curves)

        # Check which required curves are available
        curves_found = {}
        for required_curve in all_required:
            if required_curve in curve_mapping:
                curves_found[required_curve] = curve_mapping[required_curve]
            else:
                curves_found[required_curve] = None

        well_info['curve_mapping'] = curves_found
        well_info['required_curves_available'] = sum(1 for v in curves_found.values() if v is not None)
        well_info['total_required_curves'] = len(all_required)
        well_info['usable_for_training'] = well_info['required_curves_available'] == well_info['total_required_curves']

        # Calculate data quality metrics
        non_null_counts = data.count()
        total_possible = len(data) * len(data.columns)
        total_non_null = non_null_counts.sum()
        well_info['data_completeness'] = (total_non_null / total_possible) * 100 if total_possible > 0 else 0

        # Calculate quality score for required curves only
        if well_info['usable_for_training']:
            required_data_quality = []
            for curve, mapped_name in curves_found.items():
                if mapped_name and mapped_name in data.columns:
                    completeness = (data[mapped_name].count() / len(data)) * 100
                    required_data_quality.append(completeness)

            well_info['data_quality_score'] = np.mean(required_data_quality) if required_data_quality else 0
        else:
            well_info['data_quality_score'] = 0

        # Additional statistics
        well_info['non_null_counts'] = non_null_counts.to_dict()
        well_info['missing_required_curves'] = [k for k, v in curves_found.items() if v is None]

        return well_info

    def _process_wells_with_tracking(self, processor: 'WellLogDataProcessor') -> tuple:
        """Process wells individually and track their contributions."""
        all_features = []
        all_targets = []
        successful_files = []
        well_contributions = {}
        self.well_data_individual = {}  # Store individual well data for plotting

        # Sort LAS files by well name for consistent ordering
        sorted_las_files = sorted(self.las_files, key=lambda x: os.path.basename(x))

        for las_file in sorted_las_files:
            well_name = os.path.basename(las_file)

            try:
                # Process single well
                features, targets = processor.process_single_well(las_file)

                if features is not None and targets is not None and len(features) > 0:
                    all_features.append(features)
                    all_targets.append(targets)
                    successful_files.append(las_file)

                    # Track contribution
                    well_contributions[well_name] = {
                        'data_points': len(features),
                        'depth_range': (
                            self.well_info[well_name]['depth_range'][0],
                            self.well_info[well_name]['depth_range'][1]
                        ) if well_name in self.well_info else (0, 0),
                        'file_path': las_file,
                        'start_index': sum(len(f) for f in all_features[:-1]),  # Track where this well's data starts
                        'end_index': sum(len(f) for f in all_features)  # Track where this well's data ends
                    }

                    # Store individual well data for plotting
                    self.well_data_individual[well_name] = {
                        'features': features,
                        'targets': targets,
                        'depth_range': well_contributions[well_name]['depth_range'],
                        'file_path': las_file
                    }

                    print(f"    {well_name}: {len(features):,} data points processed")
                else:
                    print(f"    {well_name}: No valid data extracted")

            except Exception as e:
                print(f"    {well_name}: Error processing - {e}")

        if not all_features:
            return None, None, [], {}

        # Combine all features and targets
        combined_features = np.concatenate(all_features, axis=0)
        combined_targets = np.concatenate(all_targets, axis=0)

        # Calculate percentages
        total_points = len(combined_features)
        for well_name in well_contributions:
            well_contributions[well_name]['percentage'] = (
                well_contributions[well_name]['data_points'] / total_points * 100
            )

        return combined_features, combined_targets, successful_files, well_contributions

    def _process_data(self) -> tuple:
        """Process all LAS files and combine data."""
        processor = WellLogDataProcessor(
            target_curves=self.config['data']['target'],
            feature_curves=self.config['data']['features']
        )

        # Process wells individually to track contributions
        features, targets, successful_files, well_contributions = self._process_wells_with_tracking(processor)

        if features is None or targets is None:
            raise ValueError("Failed to process any LAS files successfully")

        print(f"  Combined features shape: {features.shape}")
        print(f"  Combined targets shape: {targets.shape}")
        print(f"  Successfully processed: {len(successful_files)} files")
        print(f"  Total data points available: {len(features)}")

        # Report well contributions
        print(f"\n  Well Contributions:")
        for well_name, contribution in well_contributions.items():
            print(f"    {well_name}: {contribution['data_points']:,} points "
                  f"({contribution['percentage']:.1f}%) - "
                  f"Depth: {contribution['depth_range'][0]:.1f}-{contribution['depth_range'][1]:.1f}m")

        # Calculate approximate depth coverage
        if len(features) > 0:
            # Assuming 0.5m depth increment (typical for LAS files)
            depth_coverage = len(features) * 0.5
            print(f"  Approximate total depth coverage: {depth_coverage:.1f} meters")

        # Check for any invalid values
        nan_features = np.isnan(features).sum()
        inf_features = np.isinf(features).sum()
        nan_targets = np.isnan(targets).sum()
        inf_targets = np.isinf(targets).sum()

        print(f"  Features - NaN count: {nan_features}")
        print(f"  Features - Inf count: {inf_features}")
        print(f"  Targets - NaN count: {nan_targets}")
        print(f"  Targets - Inf count: {inf_targets}")

        # Clean data if necessary
        if nan_features > 0 or inf_features > 0 or nan_targets > 0 or inf_targets > 0:
            print("  Cleaning invalid values...")
            valid_mask = (
                ~np.isnan(features).any(axis=1) &
                ~np.isinf(features).any(axis=1) &
                ~np.isnan(targets).any(axis=1) &
                ~np.isinf(targets).any(axis=1)
            )
            features = features[valid_mask]
            targets = targets[valid_mask]
            print(f"  After cleaning - Features shape: {features.shape}")
            print(f"  After cleaning - Targets shape: {targets.shape}")

        # Apply log transforms if configured
        if 'apply_log_transform' in self.config['data']:
            print("  Applying log transforms...")
            # Convert to DataFrame for log transform
            feature_names = self.config['data']['features']
            features_df = pd.DataFrame(features, columns=feature_names)
            features_df = apply_log_transforms(features_df, self.config)
            features = features_df.values
            print(f"  After log transforms - Features shape: {features.shape}")

        return features, targets, successful_files

    def _prepare_sequences(self, features: np.ndarray, targets: np.ndarray) -> tuple:
        """Create sequences and preprocess data."""
        processor = WellLogDataProcessor()

        # Ensure we have enough data for sequences
        min_length = self.config['data']['sequence_length']
        if len(features) < min_length:
            print(f"  Warning: Not enough data for sequences. Have {len(features)}, need {min_length}")
            # Adjust sequence length if necessary
            self.config['data']['sequence_length'] = max(10, len(features) // 2)
            print(f"  Adjusted sequence length to: {self.config['data']['sequence_length']}")

        # Create sequences
        seq_features, seq_targets = processor.create_sequences(
            features, targets,
            sequence_length=self.config['data']['sequence_length'],
            stride=self.config['data']['sequence_stride']
        )

        if len(seq_features) == 0:
            raise ValueError("No sequences could be created from the data")

        print(f"  Created {len(seq_features)} sequences")
        print(f"  Sequence features shape: {seq_features.shape}")
        print(f"  Sequence targets shape: {seq_targets.shape}")

        # Calculate data utilization
        total_data_points = len(features)
        sequence_length = self.config['data']['sequence_length']
        stride = self.config['data']['sequence_stride']

        # Calculate how much of the original data is being used
        last_sequence_end = (len(seq_features) - 1) * stride + sequence_length
        data_utilization = min(last_sequence_end / total_data_points * 100, 100)

        print(f"  Data utilization: {data_utilization:.1f}% of original data")
        print(f"  Sequence parameters: length={sequence_length}, stride={stride}")

        # Estimate depth coverage for sequences
        if total_data_points > 0:
            sequence_depth_coverage = len(seq_features) * stride * 0.5  # 0.5m typical increment
            print(f"  Sequence depth coverage: ~{sequence_depth_coverage:.1f} meters")

        # Preprocess data
        preprocessor = WellLogPreprocessor(
            normalization=self.config['data']['normalization'],
            feature_engineering=True
        )

        seq_features_norm, seq_targets_norm = preprocessor.fit_transform(
            seq_features, seq_targets,
            feature_names=self.config['data']['features'],
            target_names=self.config['data']['target']
        )

        print(f"  After preprocessing - Features shape: {seq_features_norm.shape}")
        print(f"  After preprocessing - Targets shape: {seq_targets_norm.shape}")

        return seq_features_norm, seq_targets_norm, preprocessor

    def _split_data(self, features: np.ndarray, targets: np.ndarray) -> tuple:
        """Split data into train/val/test sets and track well usage."""
        data_splits = split_data(
            features, targets,
            train_ratio=0.7,
            val_ratio=0.15,
            random_state=42
        )

        train_features, val_features, test_features, train_targets, val_targets, test_targets = data_splits

        print(f"  Train set: {train_features.shape[0]} samples")
        print(f"  Val set: {val_features.shape[0]} samples")
        print(f"  Test set: {test_features.shape[0]} samples")

        # Track well usage in splits (simplified approach)
        # Note: This is an approximation since sequences may span multiple wells
        total_sequences = len(features)
        train_end = len(train_features)
        val_end = train_end + len(val_features)

        self.well_usage = {
            'train': {
                'sequence_range': (0, train_end),
                'num_sequences': len(train_features),
                'percentage': len(train_features) / total_sequences * 100
            },
            'validation': {
                'sequence_range': (train_end, val_end),
                'num_sequences': len(val_features),
                'percentage': len(val_features) / total_sequences * 100
            },
            'test': {
                'sequence_range': (val_end, total_sequences),
                'num_sequences': len(test_features),
                'percentage': len(test_features) / total_sequences * 100
            }
        }

        print(f"  Data split percentages:")
        print(f"    Train: {self.well_usage['train']['percentage']:.1f}%")
        print(f"    Validation: {self.well_usage['validation']['percentage']:.1f}%")
        print(f"    Test: {self.well_usage['test']['percentage']:.1f}%")

        return data_splits

    def _setup_model(self, input_dim: int, preprocessor=None) -> tuple:
        """Setup model, trainer, and loss function."""
        # Update input dimension based on preprocessed features
        self.config['model']['params']['input_dim'] = input_dim

        # Create rock physics model
        rock_physics_model = RockPhysicsFactory.create(
            self.config['rock_physics']['model_type'],
            **self.config['rock_physics']['params']
        )

        # Create strategy handler
        strategy = PhysicsGuidanceStrategy(self.config['training']['strategy'])
        strategy_handler = StrategyHandler(strategy, rock_physics_model)

        # Create neural network
        model = BiGRU(**self.config['model']['params'])

        # Create trainer
        trainer = PhysicsGuidedTrainer(
            model=model,
            rock_physics_model=rock_physics_model,
            strategy_handler=strategy_handler,
            config=self.config,
            preprocessor=preprocessor
        )

        # Get loss function
        loss_fn = strategy_handler.get_loss_function()

        print(f"  Model input dimension: {input_dim}")
        print(f"  Model parameters: {sum(p.numel() for p in model.parameters())}")
        print(f"  Training device: {trainer.device}")
        print(f"  Physics guidance strategy: {self.config['training']['strategy']}")

        return model, trainer, loss_fn

    def _train_model(self, model, trainer, loss_fn, train_features, train_targets, val_features, val_targets) -> dict:
        """Train the model."""
        # Create data loaders
        train_dataset = TensorDataset(
            torch.FloatTensor(train_features),
            torch.FloatTensor(train_targets)
        )
        val_dataset = TensorDataset(
            torch.FloatTensor(val_features),
            torch.FloatTensor(val_targets)
        )

        train_loader = DataLoader(
            train_dataset,
            batch_size=self.config['training']['batch_size'],
            shuffle=True
        )
        val_loader = DataLoader(
            val_dataset,
            batch_size=self.config['training']['batch_size'],
            shuffle=False
        )

        # Setup optimizer
        optimizer = torch.optim.Adam(
            model.parameters(),
            lr=self.config['training']['learning_rate']
        )

        # Training loop
        history = {
            'train_loss': [],
            'val_loss': [],
            'val_rmse': [],
            'val_correlation': []
        }

        best_val_loss = float('inf')
        patience_counter = 0

        print(f"  Starting training for {self.config['training']['epochs']} epochs...")

        # Create progress bar
        pbar = tqdm(range(self.config['training']['epochs']), desc="Training")

        for epoch in pbar:
            # Train
            train_loss = trainer.train_epoch(train_loader, optimizer, loss_fn)

            # Validate
            val_metrics = trainer.evaluate(val_loader, torch.nn.MSELoss())

            history['train_loss'].append(train_loss)
            history['val_loss'].append(val_metrics['loss'])
            history['val_rmse'].append(val_metrics['rmse'])
            history['val_correlation'].append(val_metrics['correlation'])

            # Update progress bar
            pbar.set_postfix({
                'Train Loss': f'{train_loss:.4f}',
                'Val Loss': f'{val_metrics["loss"]:.4f}',
                'Val RMSE': f'{val_metrics["rmse"]:.4f}',
                'Val Corr': f'{val_metrics["correlation"]:.4f}'
            })

            if epoch % 10 == 0:  # Print every 10 epochs
                print(f"\n    Epoch {epoch+1:3d}/{self.config['training']['epochs']} | "
                      f"Train Loss: {train_loss:.4f} | "
                      f"Val Loss: {val_metrics['loss']:.4f} | "
                      f"Val RMSE: {val_metrics['rmse']:.4f} | "
                      f"Val Corr: {val_metrics['correlation']:.4f}")

            # Early stopping
            if val_metrics['loss'] < best_val_loss:
                best_val_loss = val_metrics['loss']
                patience_counter = 0
                # Save best model
                model_path = self.output_dir / "models" / "best_las_model.pth"
                torch.save(model.state_dict(), model_path)
            else:
                patience_counter += 1
                if patience_counter >= self.config['training']['early_stopping_patience']:
                    print(f"\n    Early stopping at epoch {epoch+1}")
                    break

        pbar.close()

        # Load best model
        model_path = self.output_dir / "models" / "best_las_model.pth"
        model.load_state_dict(torch.load(model_path, weights_only=True))

        history['final_epoch'] = epoch + 1
        history['best_val_loss'] = best_val_loss

        return history

    def _evaluate_model(self, model, test_features, test_targets, preprocessor) -> dict:
        """Evaluate model on test set."""
        model.eval()

        with torch.no_grad():
            test_features_tensor = torch.FloatTensor(test_features)
            # Move tensor to same device as model
            if torch.cuda.is_available() and next(model.parameters()).is_cuda:
                test_features_tensor = test_features_tensor.cuda()
            predictions = model(test_features_tensor).cpu().numpy()

        # Inverse transform to original scale
        predictions_orig = preprocessor.inverse_transform_targets(predictions)
        targets_orig = preprocessor.inverse_transform_targets(test_targets)

        # Calculate metrics
        predictions_flat = predictions_orig.reshape(-1)
        targets_flat = targets_orig.reshape(-1)

        # Remove NaN values if any
        valid_mask = ~(np.isnan(predictions_flat) | np.isnan(targets_flat))
        predictions_clean = predictions_flat[valid_mask]
        targets_clean = targets_flat[valid_mask]

        metrics = {
            'mse': mean_squared_error(targets_clean, predictions_clean),
            'rmse': np.sqrt(mean_squared_error(targets_clean, predictions_clean)),
            'mae': mean_absolute_error(targets_clean, predictions_clean),
            'r2': r2_score(targets_clean, predictions_clean),
            'correlation': np.corrcoef(predictions_clean, targets_clean)[0, 1],
            'mean_target': np.mean(targets_clean),
            'std_target': np.std(targets_clean),
            'mean_prediction': np.mean(predictions_clean),
            'std_prediction': np.std(predictions_clean)
        }

        print(f"  Test Results:")
        print(f"    RMSE: {metrics['rmse']:.4f}")
        print(f"    MAE: {metrics['mae']:.4f}")
        print(f"    R²: {metrics['r2']:.4f}")
        print(f"    Correlation: {metrics['correlation']:.4f}")

        return metrics

    def _test_physics_guidance(self, test_features, test_targets) -> dict:
        """Test physics guidance by comparing with mudrock line predictions."""
        try:
            # Create rock physics model
            rock_physics_model = RockPhysicsFactory.create('mudrock_line')

            # Get the preprocessor to properly handle denormalization
            processor = WellLogDataProcessor()

            # We need to get the original P-wave data for physics predictions
            # The first feature should be P-WAVE based on config
            feature_names = self.config['data']['features']
            if 'P-WAVE' not in feature_names:
                print("  Warning: P-WAVE not found in features, using first feature as VP")
                vp_feature_idx = 0
            else:
                vp_feature_idx = feature_names.index('P-WAVE')

            # Extract VP from test features (normalized)
            vp_normalized = test_features[:, :, vp_feature_idx]

            # For proper physics predictions, we need to denormalize VP to physical units
            # This is a simplified approach - in practice, we'd use the actual preprocessor
            # Assuming VP is typically in range 2000-6000 m/s, normalized to [0,1]
            vp_min, vp_max = 2000, 6000  # Typical VP range in m/s
            vp_physical = vp_normalized * (vp_max - vp_min) + vp_min

            # Convert to km/s for mudrock line (standard units)
            vp_kms = vp_physical / 1000.0

            # Generate physics predictions using mudrock line
            physics_predictions_kms = rock_physics_model.predict(vp_kms)

            # Convert back to m/s for comparison
            physics_predictions = physics_predictions_kms * 1000.0

            # For comparison, we also need to denormalize the targets
            # Assuming VS is typically in range 1000-3500 m/s
            vs_min, vs_max = 1000, 3500  # Typical VS range in m/s
            targets_physical = test_targets[:, :, 0] * (vs_max - vs_min) + vs_min

            # Flatten for metrics calculation
            physics_flat = physics_predictions.flatten()
            targets_flat = targets_physical.flatten()
            vp_flat = vp_physical.flatten()

            # Remove invalid values
            valid_mask = (
                ~np.isnan(physics_flat) & ~np.isnan(targets_flat) & ~np.isnan(vp_flat) &
                ~np.isinf(physics_flat) & ~np.isinf(targets_flat) & ~np.isinf(vp_flat)
            )

            physics_clean = physics_flat[valid_mask]
            targets_clean = targets_flat[valid_mask]
            vp_clean = vp_flat[valid_mask]

            if len(physics_clean) == 0:
                print("  Warning: No valid physics predictions generated")
                return {
                    'physics_rmse': float('inf'),
                    'physics_correlation': 0.0,
                    'physics_mae': float('inf'),
                    'physics_r2': 0.0,
                    'mudrock_equation': rock_physics_model.get_equation(),
                    'n_valid_points': 0,
                    'physics_predictions': np.array([]),
                    'vp_data': np.array([]),
                    'targets_data': np.array([])
                }

            # Calculate comprehensive metrics
            physics_rmse = np.sqrt(np.mean((physics_clean - targets_clean) ** 2))
            physics_mae = np.mean(np.abs(physics_clean - targets_clean))
            physics_corr = np.corrcoef(physics_clean, targets_clean)[0, 1]
            physics_r2 = physics_corr ** 2

            results = {
                'physics_rmse': physics_rmse,
                'physics_correlation': physics_corr,
                'physics_mae': physics_mae,
                'physics_r2': physics_r2,
                'mudrock_equation': rock_physics_model.get_equation(),
                'n_valid_points': len(physics_clean),
                'physics_predictions': physics_clean,
                'vp_data': vp_clean,
                'targets_data': targets_clean,
                'vp_range': (vp_clean.min(), vp_clean.max()),
                'vs_range': (targets_clean.min(), targets_clean.max()),
                'physics_vs_range': (physics_clean.min(), physics_clean.max())
            }

            print(f"  Physics Model (Mudrock Line) Results:")
            print(f"    Equation: {results['mudrock_equation']}")
            print(f"    RMSE: {results['physics_rmse']:.4f} m/s")
            print(f"    MAE: {results['physics_mae']:.4f} m/s")
            print(f"    Correlation: {results['physics_correlation']:.4f}")
            print(f"    R²: {results['physics_r2']:.4f}")
            print(f"    Valid points: {results['n_valid_points']:,}")

            return results

        except Exception as e:
            print(f"  Error in physics guidance testing: {e}")
            import traceback
            traceback.print_exc()
            return {
                'physics_rmse': float('inf'),
                'physics_correlation': 0.0,
                'physics_mae': float('inf'),
                'physics_r2': 0.0,
                'mudrock_equation': 'Error',
                'n_valid_points': 0,
                'physics_predictions': np.array([]),
                'vp_data': np.array([]),
                'targets_data': np.array([])
            }

    def _create_comprehensive_visualizations(self, data_splits, model, preprocessor, evaluation_results):
        """Create comprehensive visualization plots using all dataset."""
        try:
            print("  Creating comprehensive visualizations with full dataset...")

            # Unpack data splits
            train_features, val_features, test_features, train_targets, val_targets, test_targets = data_splits

            # Combine all data for comprehensive visualization
            all_features = np.concatenate([train_features, val_features, test_features], axis=0)
            all_targets = np.concatenate([train_targets, val_targets, test_targets], axis=0)

            # Create split labels for coloring
            n_train, n_val, n_test = len(train_features), len(val_features), len(test_features)
            split_labels = (['Train'] * n_train + ['Validation'] * n_val + ['Test'] * n_test)

            print(f"    Total data points for visualization: {len(all_features)}")
            print(f"    Train: {n_train}, Val: {n_val}, Test: {n_test}")

            # Make predictions on all data
            model.eval()
            with torch.no_grad():
                all_features_tensor = torch.FloatTensor(all_features)
                if torch.cuda.is_available() and next(model.parameters()).is_cuda:
                    all_features_tensor = all_features_tensor.cuda()
                all_predictions = model(all_features_tensor).cpu().numpy()

            # Inverse transform
            all_predictions_orig = preprocessor.inverse_transform_targets(all_predictions)
            all_targets_orig = preprocessor.inverse_transform_targets(all_targets)

            # Create comprehensive visualization with multiple subplots
            self._create_main_plots(all_predictions_orig, all_targets_orig, split_labels, evaluation_results)
            self._create_detailed_analysis_plots(all_features, all_targets, all_predictions_orig, all_targets_orig, preprocessor)
            self._create_sequence_analysis_plots(all_features, all_targets, all_predictions_orig, all_targets_orig, preprocessor)

            print("  All visualizations created successfully!")

        except Exception as e:
            print(f"  Error creating comprehensive visualizations: {e}")
            import traceback
            traceback.print_exc()

    def _create_main_plots(self, all_predictions_orig, all_targets_orig, split_labels, evaluation_results):
        """Create main analysis plots."""
        plt.style.use('default')
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))

        # Flatten data for plotting
        pred_flat = all_predictions_orig.reshape(-1)
        target_flat = all_targets_orig.reshape(-1)

        # Create split labels for each flattened point
        split_flat = []
        for i, label in enumerate(split_labels):
            split_flat.extend([label] * all_predictions_orig.shape[1])

        # Remove NaN and infinite values
        valid_mask = (
            ~np.isnan(pred_flat) &
            ~np.isnan(target_flat) &
            ~np.isinf(pred_flat) &
            ~np.isinf(target_flat)
        )
        pred_clean = pred_flat[valid_mask]
        target_clean = target_flat[valid_mask]
        split_clean = np.array(split_flat)[valid_mask]

        print(f"    Valid points for plotting: {len(pred_clean)} ({len(pred_clean)/len(pred_flat)*100:.1f}%)")

        if len(pred_clean) == 0:
            print("    Warning: No valid predictions for visualization")
            return

        # Plot 1: Predictions vs Actual (colored by dataset split)
        colors = {'Train': 'blue', 'Validation': 'orange', 'Test': 'red'}
        for split in ['Train', 'Validation', 'Test']:
            mask = split_clean == split
            if np.any(mask):
                axes[0, 0].scatter(target_clean[mask], pred_clean[mask],
                                 alpha=0.6, s=1, c=colors[split], label=split)

        # Perfect prediction line
        min_val, max_val = target_clean.min(), target_clean.max()
        axes[0, 0].plot([min_val, max_val], [min_val, max_val], 'k--', lw=2, alpha=0.8, label='Perfect Prediction')
        axes[0, 0].set_xlabel('Actual S-wave Velocity (m/s)')
        axes[0, 0].set_ylabel('Predicted S-wave Velocity (m/s)')
        axes[0, 0].set_title(f'Predictions vs Actual - Full Dataset\n(R² = {evaluation_results["r2"]:.3f}, {len(pred_clean):,} points)')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

        # Plot 2: Residuals Analysis
        residuals = pred_clean - target_clean
        for split in ['Train', 'Validation', 'Test']:
            mask = split_clean == split
            if np.any(mask):
                axes[0, 1].scatter(target_clean[mask], residuals[mask],
                                 alpha=0.6, s=1, c=colors[split], label=split)

        axes[0, 1].axhline(y=0, color='k', linestyle='--', alpha=0.8)
        axes[0, 1].set_xlabel('Actual S-wave Velocity (m/s)')
        axes[0, 1].set_ylabel('Residuals (m/s)')
        axes[0, 1].set_title(f'Residual Analysis - Full Dataset\n(RMSE = {evaluation_results["rmse"]:.3f} m/s)')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)

        # Plot 3: Training History
        if 'training' in self.results:
            history = self.results['training']
            epochs = range(1, len(history['train_loss']) + 1)
            axes[1, 0].plot(epochs, history['train_loss'], label='Training Loss', alpha=0.8, linewidth=2)
            axes[1, 0].plot(epochs, history['val_loss'], label='Validation Loss', alpha=0.8, linewidth=2)
            axes[1, 0].set_xlabel('Epoch')
            axes[1, 0].set_ylabel('Loss')
            axes[1, 0].set_title('Training History')
            axes[1, 0].legend()
            axes[1, 0].grid(True, alpha=0.3)

            # Add final values as text
            final_train_loss = history['train_loss'][-1]
            final_val_loss = history['val_loss'][-1]
            axes[1, 0].text(0.02, 0.98, f'Final Train Loss: {final_train_loss:.4f}\nFinal Val Loss: {final_val_loss:.4f}',
                           transform=axes[1, 0].transAxes, verticalalignment='top',
                           bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

        # Plot 4: Data Distribution
        axes[1, 1].hist(target_clean, bins=50, alpha=0.7, color='skyblue', edgecolor='black', label='Actual')
        axes[1, 1].hist(pred_clean, bins=50, alpha=0.7, color='lightcoral', edgecolor='black', label='Predicted')
        axes[1, 1].set_xlabel('S-wave Velocity (m/s)')
        axes[1, 1].set_ylabel('Frequency')
        axes[1, 1].set_title(f'Data Distribution Comparison\n({len(pred_clean):,} total points)')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)

        plt.tight_layout()
        main_results_path = self.output_dir / "visualizations" / "las_ml_pipeline_main_results.png"
        plt.savefig(main_results_path, dpi=300, bbox_inches='tight')
        print(f"    Main plots saved to '{main_results_path}'")
        plt.close(fig)

    def _create_detailed_analysis_plots(self, all_features, all_targets, all_predictions_orig, all_targets_orig, preprocessor):
        """Create detailed analysis plots showing feature relationships and data coverage."""
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))

        # Get feature names
        feature_names = self.config['data']['features']

        # Inverse transform ONLY the original base features for plotting
        # The preprocessor's feature scaler was fitted on the base features (len(feature_names))
        # and additional engineered features were added AFTER scaling. So we must not try to
        # inverse-transform engineered columns.
        n_base = len(feature_names)
        base_features_norm = all_features[..., :n_base]
        base_features_orig = preprocessor.scalers['features'].inverse_transform(
            base_features_norm.reshape(-1, n_base)
        ).reshape(base_features_norm.shape)

        # Flatten data (using base/original features only for feature-target plots)
        features_flat = base_features_orig.reshape(-1, n_base)
        targets_flat = all_targets_orig.reshape(-1)
        predictions_flat = all_predictions_orig.reshape(-1)

        # Remove invalid values
        valid_mask = (
            ~np.isnan(features_flat).any(axis=1) &
            ~np.isnan(targets_flat) &
            ~np.isnan(predictions_flat) &
            ~np.isinf(targets_flat) &
            ~np.isinf(predictions_flat)
        )

        features_clean = features_flat[valid_mask]
        targets_clean = targets_flat[valid_mask]
        predictions_clean = predictions_flat[valid_mask]

        print(f"    Valid points for detailed analysis: {len(features_clean):,}")

        # Plot feature vs target relationships (first 4 features)
        for i in range(min(4, len(feature_names))):
            row, col = i // 2, i % 2

            # Scatter plot of feature vs actual target
            axes[row, col].scatter(features_clean[:, i], targets_clean, alpha=0.3, s=0.5, c='blue', label='Actual')
            axes[row, col].scatter(features_clean[:, i], predictions_clean, alpha=0.3, s=0.5, c='red', label='Predicted')

            axes[row, col].set_xlabel(f'{feature_names[i]}')
            axes[row, col].set_ylabel('S-wave Velocity (m/s)')
            axes[row, col].set_title(f'{feature_names[i]} vs S-wave Velocity\n({len(features_clean):,} points)')
            axes[row, col].legend()
            axes[row, col].grid(True, alpha=0.3)

        # Plot 5: Error distribution
        errors = predictions_clean - targets_clean
        axes[1, 2].hist(errors, bins=50, alpha=0.7, color='orange', edgecolor='black')
        axes[1, 2].axvline(x=0, color='red', linestyle='--', linewidth=2)
        axes[1, 2].set_xlabel('Prediction Error (m/s)')
        axes[1, 2].set_ylabel('Frequency')
        axes[1, 2].set_title(f'Error Distribution\nMean: {np.mean(errors):.3f}, Std: {np.std(errors):.3f}')
        axes[1, 2].grid(True, alpha=0.3)

        # Add statistics text
        stats_text = f'Statistics:\nMean Error: {np.mean(errors):.3f} m/s\nStd Error: {np.std(errors):.3f} m/s\nMAE: {np.mean(np.abs(errors)):.3f} m/s\nRMSE: {np.sqrt(np.mean(errors**2)):.3f} m/s'
        axes[1, 2].text(0.02, 0.98, stats_text, transform=axes[1, 2].transAxes,
                        verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

        plt.tight_layout()
        detailed_analysis_path = self.output_dir / "visualizations" / "las_ml_pipeline_detailed_analysis.png"
        plt.savefig(detailed_analysis_path, dpi=300, bbox_inches='tight')
        print(f"    Detailed analysis plots saved to '{detailed_analysis_path}'")
        plt.close(fig)

    def _create_sequence_analysis_plots(self, all_features, all_targets, all_predictions_orig, all_targets_orig, preprocessor=None):
        """Create sequence-level analysis plots showing multiple sample predictions, including physics model."""
        fig, axes = plt.subplots(3, 2, figsize=(16, 18))

        n_sequences = len(all_features)
        sequence_length = all_features.shape[1]

        print(f"    Creating sequence analysis with {n_sequences} sequences of length {sequence_length}")

        # Prepare base (original-scale) features to compute physics predictions
        feature_names = self.config['data']['features']
        n_base = len(feature_names)
        base_features_norm = all_features[..., :n_base]
        base_features_orig = None
        if preprocessor is not None and hasattr(preprocessor, 'scalers') and 'features' in preprocessor.scalers:
            # Inverse-transform only the base features (exclude engineered ones)
            base_features_orig = preprocessor.scalers['features'].inverse_transform(
                base_features_norm.reshape(-1, n_base)
            ).reshape(base_features_norm.shape)
        else:
            # Fallback: use normalized values (will apply heuristic denorm for VP later)
            base_features_orig = base_features_norm.copy()

        # Determine VP feature index
        vp_feature_idx = feature_names.index('P-WAVE') if 'P-WAVE' in feature_names else 0

        # Physics model
        rock_physics_model = RockPhysicsFactory.create('mudrock_line')

        # Select representative sequences from different parts of the dataset
        sequence_indices = [
            0,  # First sequence
            n_sequences // 4,  # Quarter point
            n_sequences // 2,  # Middle
            3 * n_sequences // 4,  # Three-quarter point
            n_sequences - 1,  # Last sequence
            np.random.randint(0, n_sequences)  # Random sequence
        ]

        sequence_labels = [
            'First Sequence (Start of Dataset)',
            'Quarter Point Sequence',
            'Middle Sequence',
            'Three-Quarter Point Sequence',
            'Last Sequence (End of Dataset)',
            'Random Sequence'
        ]

        for i, (seq_idx, label) in enumerate(zip(sequence_indices, sequence_labels)):
            row, col = i // 2, i % 2

            if seq_idx >= n_sequences:
                seq_idx = n_sequences - 1

            # Get sequence data (actual and BiGRU predictions)
            actual_seq = all_targets_orig[seq_idx, :, 0]
            pred_seq = all_predictions_orig[seq_idx, :, 0]

            # Compute physics model predictions for this sequence
            vp_seq = base_features_orig[seq_idx, :, vp_feature_idx]
            # Heuristic: if values look normalized (0-1), denormalize to typical m/s range
            if np.nanmax(vp_seq) <= 1.5:  # likely normalized
                vp_min, vp_max = 2000.0, 6000.0
                vp_seq_mps = vp_seq * (vp_max - vp_min) + vp_min
            else:
                vp_seq_mps = vp_seq
            vp_seq_kms = vp_seq_mps / 1000.0
            physics_vs_kms = rock_physics_model.predict(vp_seq_kms)
            physics_seq = physics_vs_kms * 1000.0

            # Create depth indices (assuming 0.5m increment)
            depth_indices = np.arange(len(actual_seq)) * 0.5

            # Plot Actual, BiGRU, and Physics for this sequence (consistent styling)
            axes[row, col].plot(depth_indices, actual_seq, color='black', linestyle='-', linewidth=2, alpha=0.8, label='Actual')
            axes[row, col].plot(depth_indices, pred_seq, color='red', linestyle='--', linewidth=2, alpha=0.8, label='BiGRU')
            axes[row, col].plot(depth_indices, physics_seq, color='blue', linestyle='-.', linewidth=2, alpha=0.8, label='Physics')

            # Calculate sequence-level metrics
            seq_rmse_bigru = np.sqrt(np.mean((pred_seq - actual_seq) ** 2))
            seq_corr_bigru = np.corrcoef(actual_seq, pred_seq)[0, 1] if not np.isnan(actual_seq).any() and not np.isnan(pred_seq).any() else 0
            seq_rmse_phys = np.sqrt(np.mean((physics_seq - actual_seq) ** 2))
            seq_corr_phys = np.corrcoef(actual_seq, physics_seq)[0, 1] if not np.isnan(actual_seq).any() and not np.isnan(physics_seq).any() else 0

            axes[row, col].set_xlabel('Relative Depth (m)')
            axes[row, col].set_ylabel('S-wave Velocity (m/s)')

            # Add R² metrics
            seq_r2_bigru = (seq_corr_bigru ** 2) if not np.isnan(seq_corr_bigru) else 0.0
            seq_r2_phys = (seq_corr_phys ** 2) if not np.isnan(seq_corr_phys) else 0.0

            axes[row, col].set_title(
                f"{label}\nSequence {seq_idx+1}/{n_sequences} | "
                f"BiGRU RMSE: {seq_rmse_bigru:.3f}, R²: {seq_r2_bigru:.3f} | "
                f"Physics RMSE: {seq_rmse_phys:.3f}, R²: {seq_r2_phys:.3f}"
            )

            # Compact metrics box (bottom-right)
            metrics_text = (
                f"BiGRU  -> RMSE: {seq_rmse_bigru:.2f}, R²: {seq_r2_bigru:.3f}, Corr: {seq_corr_bigru:.3f}\n"
                f"Physics-> RMSE: {seq_rmse_phys:.2f}, R²: {seq_r2_phys:.3f}, Corr: {seq_corr_phys:.3f}"
            )
            axes[row, col].text(0.98, 0.02, metrics_text, transform=axes[row, col].transAxes,
                               ha='right', va='bottom', fontsize=8,
                               bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))

            axes[row, col].legend()
            axes[row, col].grid(True, alpha=0.3)

            # Add range information
            actual_range = f'{np.nanmin(actual_seq):.0f}-{np.nanmax(actual_seq):.0f}'
            pred_range = f'{np.nanmin(pred_seq):.0f}-{np.nanmax(pred_seq):.0f}'
            phys_range = f'{np.nanmin(physics_seq):.0f}-{np.nanmax(physics_seq):.0f}'
            range_text = (
                f'Actual: {actual_range} m/s\n'
                f'BiGRU:  {pred_range} m/s\n'
                f'Phys:   {phys_range} m/s'
            )
            axes[row, col].text(0.02, 0.98, range_text, transform=axes[row, col].transAxes,
                               verticalalignment='top', fontsize=8,
                               bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

        plt.tight_layout()
        sequence_analysis_path = self.output_dir / "visualizations" / "las_ml_pipeline_sequence_analysis.png"
        plt.savefig(sequence_analysis_path, dpi=300, bbox_inches='tight')
        print(f"    Sequence analysis plots saved to '{sequence_analysis_path}'")
        plt.close(fig)

        # Create additional data coverage plot
        self._create_data_coverage_plot(all_features, all_targets, all_predictions_orig, all_targets_orig)

        # Create interactive log display if enabled
        if self.enable_interactive:
            self._create_interactive_log_display(all_features, all_targets, all_predictions_orig, all_targets_orig, preprocessor)

    def _create_data_coverage_plot(self, all_features, all_targets, all_predictions_orig, all_targets_orig):
        """Create a plot showing data coverage and utilization across the full dataset."""
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))

        n_sequences = len(all_features)
        sequence_length = all_features.shape[1]

        # Create sequence indices for x-axis
        sequence_indices = np.arange(n_sequences)

        # Plot 1: Data coverage heatmap
        targets_2d = all_targets_orig[:, :, 0]  # Shape: (n_sequences, sequence_length)

        # Create a heatmap showing data availability
        im1 = axes[0, 0].imshow(targets_2d.T, aspect='auto', cmap='viridis', interpolation='nearest')
        axes[0, 0].set_xlabel('Sequence Index')
        axes[0, 0].set_ylabel('Depth Index within Sequence')
        axes[0, 0].set_title(f'Data Coverage Heatmap - Actual S-wave Velocity\n({n_sequences} sequences × {sequence_length} points each)')
        plt.colorbar(im1, ax=axes[0, 0], label='S-wave Velocity (m/s)')

        # Plot 2: Prediction coverage heatmap
        predictions_2d = all_predictions_orig[:, :, 0]
        im2 = axes[0, 1].imshow(predictions_2d.T, aspect='auto', cmap='plasma', interpolation='nearest')
        axes[0, 1].set_xlabel('Sequence Index')
        axes[0, 1].set_ylabel('Depth Index within Sequence')
        axes[0, 1].set_title(f'Prediction Coverage Heatmap - Predicted S-wave Velocity\n({n_sequences} sequences × {sequence_length} points each)')
        plt.colorbar(im2, ax=axes[0, 1], label='S-wave Velocity (m/s)')

        # Plot 3: Sequence-wise statistics
        seq_means_actual = np.mean(targets_2d, axis=1)
        seq_means_pred = np.mean(predictions_2d, axis=1)
        seq_stds_actual = np.std(targets_2d, axis=1)
        seq_stds_pred = np.std(predictions_2d, axis=1)

        axes[1, 0].plot(sequence_indices, seq_means_actual, 'b-', alpha=0.7, label='Actual Mean', linewidth=1)
        axes[1, 0].plot(sequence_indices, seq_means_pred, 'r-', alpha=0.7, label='Predicted Mean', linewidth=1)
        axes[1, 0].fill_between(sequence_indices,
                               seq_means_actual - seq_stds_actual,
                               seq_means_actual + seq_stds_actual,
                               alpha=0.2, color='blue', label='Actual ±1σ')
        axes[1, 0].fill_between(sequence_indices,
                               seq_means_pred - seq_stds_pred,
                               seq_means_pred + seq_stds_pred,
                               alpha=0.2, color='red', label='Predicted ±1σ')

        axes[1, 0].set_xlabel('Sequence Index')
        axes[1, 0].set_ylabel('S-wave Velocity (m/s)')
        axes[1, 0].set_title('Sequence-wise Statistics Across Full Dataset')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)

        # Plot 4: Data utilization summary
        total_possible_points = n_sequences * sequence_length
        valid_actual = ~np.isnan(targets_2d)
        valid_pred = ~np.isnan(predictions_2d)

        utilization_data = [
            valid_actual.sum(),
            valid_pred.sum(),
            total_possible_points
        ]

        labels = ['Valid Actual\nData Points', 'Valid Predicted\nData Points', 'Total Possible\nData Points']
        colors = ['skyblue', 'lightcoral', 'lightgray']

        bars = axes[1, 1].bar(labels, utilization_data, color=colors, alpha=0.8, edgecolor='black')
        axes[1, 1].set_ylabel('Number of Data Points')
        axes[1, 1].set_title('Data Utilization Summary')
        axes[1, 1].grid(True, alpha=0.3, axis='y')

        # Add value labels on bars
        for bar, value in zip(bars, utilization_data):
            height = bar.get_height()
            axes[1, 1].text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                           f'{value:,}\n({value/total_possible_points*100:.1f}%)',
                           ha='center', va='bottom', fontweight='bold')

        # Add summary statistics
        coverage_text = f'Dataset Summary:\n'
        coverage_text += f'Total Sequences: {n_sequences:,}\n'
        coverage_text += f'Sequence Length: {sequence_length}\n'
        coverage_text += f'Total Data Points: {total_possible_points:,}\n'
        coverage_text += f'Data Utilization: {valid_actual.sum()/total_possible_points*100:.1f}%\n'
        coverage_text += f'Approx. Depth Coverage: {n_sequences * sequence_length * 0.5:.0f}m'

        axes[1, 1].text(0.02, 0.98, coverage_text, transform=axes[1, 1].transAxes,
                       verticalalignment='top', fontsize=10,
                       bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.9))

        plt.tight_layout()
        data_coverage_path = self.output_dir / "visualizations" / "las_ml_pipeline_data_coverage.png"
        plt.savefig(data_coverage_path, dpi=300, bbox_inches='tight')
        print(f"    Data coverage plots saved to '{data_coverage_path}'")
        plt.close(fig)

    def _create_interactive_log_display(self, all_features, all_targets, all_predictions_orig, all_targets_orig, preprocessor):
        """Create interactive matplotlib display for well log analysis."""
        print("    Creating interactive log display...")

        try:
            # Set up interactive backend
            plt.ion()  # Turn on interactive mode

            # Create the interactive figure
            fig, axes = plt.subplots(1, 3, figsize=(18, 10))
            fig.suptitle('Interactive Well Log Display - S-wave Velocity Prediction', fontsize=16, fontweight='bold')

            # Flatten data for easier handling
            targets_flat = all_targets_orig.reshape(-1)
            predictions_flat = all_predictions_orig.reshape(-1)

            # Remove invalid values
            valid_mask = (
                ~np.isnan(targets_flat) &
                ~np.isnan(predictions_flat) &
                ~np.isinf(targets_flat) &
                ~np.isinf(predictions_flat)
            )

            targets_clean = targets_flat[valid_mask]
            predictions_clean = predictions_flat[valid_mask]

            # Create depth indices (assuming 0.5m increment)
            depth_indices = np.arange(len(targets_clean)) * 0.5

            # Plot 1: Complete log display
            axes[0].plot(targets_clean, depth_indices, 'b-', linewidth=1, alpha=0.8, label='Actual S-wave')
            axes[0].plot(predictions_clean, depth_indices, 'r--', linewidth=1, alpha=0.8, label='Predicted S-wave')
            axes[0].set_xlabel('S-wave Velocity (m/s)')
            axes[0].set_ylabel('Depth (m)')
            axes[0].set_title('Complete Well Log Display')
            axes[0].legend()
            axes[0].grid(True, alpha=0.3)
            axes[0].invert_yaxis()  # Invert y-axis for traditional well log display

            # Plot 2: Zoomed section (middle portion)
            zoom_start = len(targets_clean) // 3
            zoom_end = 2 * len(targets_clean) // 3
            zoom_targets = targets_clean[zoom_start:zoom_end]
            zoom_predictions = predictions_clean[zoom_start:zoom_end]
            zoom_depths = depth_indices[zoom_start:zoom_end]

            axes[1].plot(zoom_targets, zoom_depths, 'b-', linewidth=2, alpha=0.8, label='Actual S-wave')
            axes[1].plot(zoom_predictions, zoom_depths, 'r--', linewidth=2, alpha=0.8, label='Predicted S-wave')
            axes[1].set_xlabel('S-wave Velocity (m/s)')
            axes[1].set_ylabel('Depth (m)')
            axes[1].set_title(f'Zoomed Section ({zoom_depths[0]:.0f}-{zoom_depths[-1]:.0f}m)')
            axes[1].legend()
            axes[1].grid(True, alpha=0.3)
            axes[1].invert_yaxis()

            # Plot 3: Error analysis by depth
            errors = predictions_clean - targets_clean
            axes[2].plot(errors, depth_indices, 'g-', linewidth=1, alpha=0.7)
            axes[2].axvline(x=0, color='k', linestyle='--', alpha=0.8)
            axes[2].set_xlabel('Prediction Error (m/s)')
            axes[2].set_ylabel('Depth (m)')
            axes[2].set_title('Prediction Error vs Depth')
            axes[2].grid(True, alpha=0.3)
            axes[2].invert_yaxis()

            # Add statistics text
            stats_text = f'Interactive Log Statistics:\n'
            stats_text += f'Total depth: {depth_indices[-1]:.0f}m\n'
            stats_text += f'Data points: {len(targets_clean):,}\n'
            stats_text += f'RMSE: {np.sqrt(np.mean(errors**2)):.3f} m/s\n'
            stats_text += f'Mean error: {np.mean(errors):.3f} m/s\n'
            stats_text += f'Correlation: {np.corrcoef(targets_clean, predictions_clean)[0,1]:.3f}'

            fig.text(0.02, 0.02, stats_text, fontsize=10,
                    bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

            # Add interaction instructions
            instructions = 'Interactive Features:\n• Zoom: Mouse wheel or toolbar\n• Pan: Click and drag\n• Reset: Home button in toolbar'
            fig.text(0.98, 0.02, instructions, fontsize=9, ha='right',
                    bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

            plt.tight_layout()

            # Save the interactive plot
            interactive_path = self.output_dir / "visualizations" / "interactive_log_display.png"
            plt.savefig(interactive_path, dpi=300, bbox_inches='tight')
            print(f"    Interactive log display saved to '{interactive_path}'")

            # Show interactive plot if possible
            try:
                print("    Displaying interactive plot... (Close the window to continue)")
                print("    Use mouse wheel to zoom, click and drag to pan")
                plt.show(block=True)  # Block until user closes the window
            except Exception as e:
                print(f"    Could not display interactive plot: {e}")
                print("    Static version saved to file")

            plt.close(fig)
            plt.ioff()  # Turn off interactive mode

        except Exception as e:
            print(f"    Error creating interactive log display: {e}")
            print("    Continuing without interactive display...")

    def _create_physics_comparison_plots(self, data_splits, model, preprocessor, evaluation_results, physics_results):
        """Create comprehensive physics-informed comparison plots."""
        try:
            print("  Creating physics-informed comparison plots...")

            # Unpack data splits
            train_features, val_features, test_features, train_targets, val_targets, test_targets = data_splits

            # Use test data for physics comparison
            print(f"    Using {len(test_features)} test sequences for physics comparison")

            # Generate BiGRU predictions on test data
            model.eval()
            with torch.no_grad():
                test_features_tensor = torch.FloatTensor(test_features)
                if torch.cuda.is_available() and next(model.parameters()).is_cuda:
                    test_features_tensor = test_features_tensor.cuda()
                bigru_predictions = model(test_features_tensor).cpu().numpy()

            # Inverse transform BiGRU predictions and targets to original scale
            bigru_predictions_orig = preprocessor.inverse_transform_targets(bigru_predictions)
            test_targets_orig = preprocessor.inverse_transform_targets(test_targets)

            # Get physics predictions from the physics_results
            if 'physics_predictions' in physics_results and len(physics_results['physics_predictions']) > 0:
                physics_predictions = physics_results['physics_predictions']
                vp_data = physics_results['vp_data']
                targets_data = physics_results['targets_data']

                # Create comprehensive comparison plots
                self._plot_physics_vs_bigru_comparison(
                    targets_data, bigru_predictions_orig, physics_predictions,
                    vp_data, physics_results, evaluation_results
                )

                # Create detailed analysis plots
                self._plot_physics_detailed_analysis(
                    targets_data, bigru_predictions_orig, physics_predictions,
                    vp_data, physics_results, evaluation_results
                )

                print("  Physics comparison plots created successfully!")
            else:
                print("  Warning: No valid physics predictions available for comparison plots")

        except Exception as e:
            print(f"  Error creating physics comparison plots: {e}")
            import traceback
            traceback.print_exc()

    def _plot_physics_vs_bigru_comparison(self, targets_data, bigru_predictions_orig, physics_predictions,
                                        vp_data, physics_results, evaluation_results):
        """Create main physics vs BiGRU comparison plot."""
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Physics-Informed Model Comparison: Mudrock Line vs BiGRU vs Real Data',
                    fontsize=16, fontweight='bold')

        # Flatten BiGRU predictions for comparison (sample same number of points as physics)
        bigru_flat = bigru_predictions_orig.reshape(-1)
        n_physics = len(physics_predictions)

        # Sample BiGRU predictions to match physics predictions length
        if len(bigru_flat) > n_physics:
            sample_indices = np.linspace(0, len(bigru_flat)-1, n_physics, dtype=int)
            bigru_sampled = bigru_flat[sample_indices]
        else:
            bigru_sampled = bigru_flat[:n_physics]

        # Ensure all arrays have the same length
        min_length = min(len(targets_data), len(bigru_sampled), len(physics_predictions))
        targets_clean = targets_data[:min_length]
        bigru_clean = bigru_sampled[:min_length]
        physics_clean = physics_predictions[:min_length]
        vp_clean = vp_data[:min_length]

        print(f"    Comparing {min_length:,} data points across all methods")

        # Plot 1: Three-way scatter comparison
        axes[0, 0].scatter(targets_clean, bigru_clean, alpha=0.6, s=2, c='red', label='BiGRU vs Real')
        axes[0, 0].scatter(targets_clean, physics_clean, alpha=0.6, s=2, c='blue', label='Physics vs Real')

        # Perfect prediction line
        min_val, max_val = targets_clean.min(), targets_clean.max()
        axes[0, 0].plot([min_val, max_val], [min_val, max_val], 'k--', lw=2, alpha=0.8, label='Perfect Prediction')

        axes[0, 0].set_xlabel('Real S-wave Velocity (m/s)', fontweight='bold')
        axes[0, 0].set_ylabel('Predicted S-wave Velocity (m/s)', fontweight='bold')
        axes[0, 0].set_title('Predictions vs Real Data Comparison', fontweight='bold')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

        # Plot 2: Direct BiGRU vs Physics comparison
        axes[0, 1].scatter(physics_clean, bigru_clean, alpha=0.6, s=2, c='green')
        min_val, max_val = min(physics_clean.min(), bigru_clean.min()), max(physics_clean.max(), bigru_clean.max())
        axes[0, 1].plot([min_val, max_val], [min_val, max_val], 'k--', lw=2, alpha=0.8, label='Perfect Agreement')

        axes[0, 1].set_xlabel('Physics Model Prediction (m/s)', fontweight='bold')
        axes[0, 1].set_ylabel('BiGRU Model Prediction (m/s)', fontweight='bold')
        axes[0, 1].set_title('BiGRU vs Physics Model Comparison', fontweight='bold')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)

        # Plot 3: VP vs VS relationships
        axes[1, 0].scatter(vp_clean, targets_clean, alpha=0.6, s=2, c='black', label='Real Data')
        axes[1, 0].scatter(vp_clean, physics_clean, alpha=0.6, s=2, c='blue', label='Physics Model')
        axes[1, 0].scatter(vp_clean, bigru_clean, alpha=0.6, s=2, c='red', label='BiGRU Model')

        axes[1, 0].set_xlabel('P-wave Velocity (m/s)', fontweight='bold')
        axes[1, 0].set_ylabel('S-wave Velocity (m/s)', fontweight='bold')
        axes[1, 0].set_title('VP-VS Relationships Comparison', fontweight='bold')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)

        # Plot 4: Performance metrics comparison
        axes[1, 1].axis('off')

        # Calculate BiGRU metrics for comparison
        bigru_rmse = np.sqrt(np.mean((bigru_clean - targets_clean) ** 2))
        bigru_mae = np.mean(np.abs(bigru_clean - targets_clean))
        bigru_corr = np.corrcoef(bigru_clean, targets_clean)[0, 1]
        bigru_r2 = bigru_corr ** 2

        # Performance comparison text
        comparison_text = f'📊 PERFORMANCE COMPARISON\n'
        comparison_text += f'{"="*35}\n\n'

        comparison_text += f'🤖 BiGRU Model:\n'
        comparison_text += f'  RMSE: {bigru_rmse:.3f} m/s\n'
        comparison_text += f'  MAE:  {bigru_mae:.3f} m/s\n'
        comparison_text += f'  R²:   {bigru_r2:.4f}\n'
        comparison_text += f'  Corr: {bigru_corr:.4f}\n\n'

        comparison_text += f'⚗️ Physics Model (Mudrock Line):\n'
        comparison_text += f'  RMSE: {physics_results["physics_rmse"]:.3f} m/s\n'
        comparison_text += f'  MAE:  {physics_results["physics_mae"]:.3f} m/s\n'
        comparison_text += f'  R²:   {physics_results["physics_r2"]:.4f}\n'
        comparison_text += f'  Corr: {physics_results["physics_correlation"]:.4f}\n\n'

        comparison_text += f'📈 Data Summary:\n'
        comparison_text += f'  Equation: {physics_results["mudrock_equation"]}\n'
        comparison_text += f'  Data Points: {min_length:,}\n'
        comparison_text += f'  VP Range: {vp_clean.min():.0f}-{vp_clean.max():.0f} m/s\n'
        comparison_text += f'  VS Range: {targets_clean.min():.0f}-{targets_clean.max():.0f} m/s\n\n'

        # Performance winner
        if bigru_rmse < physics_results["physics_rmse"]:
            comparison_text += f'🏆 Best RMSE: BiGRU Model\n'
        else:
            comparison_text += f'🏆 Best RMSE: Physics Model\n'

        if bigru_r2 > physics_results["physics_r2"]:
            comparison_text += f'🏆 Best R²: BiGRU Model'
        else:
            comparison_text += f'🏆 Best R²: Physics Model'

        axes[1, 1].text(0.05, 0.95, comparison_text, transform=axes[1, 1].transAxes,
                       verticalalignment='top', fontsize=10, fontfamily='monospace',
                       bbox=dict(boxstyle='round,pad=0.5', facecolor='lightgreen', alpha=0.9, edgecolor='darkgreen'))

        plt.tight_layout()

        # Save plot
        physics_comparison_path = self.output_dir / "visualizations" / "physics_vs_bigru_comparison.png"
        plt.savefig(physics_comparison_path, dpi=300, bbox_inches='tight')
        print(f"    Physics comparison plot saved to '{physics_comparison_path}'")
        plt.close(fig)

    def _plot_physics_detailed_analysis(self, targets_data, bigru_predictions_orig, physics_predictions,
                                      vp_data, physics_results, evaluation_results):
        """Create detailed physics analysis plots."""
        fig, axes = plt.subplots(2, 3, figsize=(20, 12))
        fig.suptitle('Detailed Physics-Informed Analysis: Model Performance and Error Analysis',
                    fontsize=16, fontweight='bold')

        # Prepare data (same length for all)
        bigru_flat = bigru_predictions_orig.reshape(-1)
        n_physics = len(physics_predictions)

        if len(bigru_flat) > n_physics:
            sample_indices = np.linspace(0, len(bigru_flat)-1, n_physics, dtype=int)
            bigru_sampled = bigru_flat[sample_indices]
        else:
            bigru_sampled = bigru_flat[:n_physics]

        min_length = min(len(targets_data), len(bigru_sampled), len(physics_predictions))
        targets_clean = targets_data[:min_length]
        bigru_clean = bigru_sampled[:min_length]
        physics_clean = physics_predictions[:min_length]
        vp_clean = vp_data[:min_length]

        # Calculate errors
        bigru_errors = bigru_clean - targets_clean
        physics_errors = physics_clean - targets_clean

        # Plot 1: Error distributions
        axes[0, 0].hist(bigru_errors, bins=50, alpha=0.7, color='red', label='BiGRU Errors', density=True)
        axes[0, 0].hist(physics_errors, bins=50, alpha=0.7, color='blue', label='Physics Errors', density=True)
        axes[0, 0].axvline(x=0, color='black', linestyle='--', alpha=0.8)
        axes[0, 0].set_xlabel('Prediction Error (m/s)', fontweight='bold')
        axes[0, 0].set_ylabel('Density', fontweight='bold')
        axes[0, 0].set_title('Error Distribution Comparison', fontweight='bold')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

        # Plot 2: Error vs VP
        axes[0, 1].scatter(vp_clean, bigru_errors, alpha=0.5, s=1, c='red', label='BiGRU Errors')
        axes[0, 1].scatter(vp_clean, physics_errors, alpha=0.5, s=1, c='blue', label='Physics Errors')
        axes[0, 1].axhline(y=0, color='black', linestyle='--', alpha=0.8)
        axes[0, 1].set_xlabel('P-wave Velocity (m/s)', fontweight='bold')
        axes[0, 1].set_ylabel('Prediction Error (m/s)', fontweight='bold')
        axes[0, 1].set_title('Error vs P-wave Velocity', fontweight='bold')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)

        # Plot 3: Error vs True VS
        axes[0, 2].scatter(targets_clean, bigru_errors, alpha=0.5, s=1, c='red', label='BiGRU Errors')
        axes[0, 2].scatter(targets_clean, physics_errors, alpha=0.5, s=1, c='blue', label='Physics Errors')
        axes[0, 2].axhline(y=0, color='black', linestyle='--', alpha=0.8)
        axes[0, 2].set_xlabel('True S-wave Velocity (m/s)', fontweight='bold')
        axes[0, 2].set_ylabel('Prediction Error (m/s)', fontweight='bold')
        axes[0, 2].set_title('Error vs True S-wave Velocity', fontweight='bold')
        axes[0, 2].legend()
        axes[0, 2].grid(True, alpha=0.3)

        # Plot 4: Cumulative error analysis
        bigru_abs_errors = np.abs(bigru_errors)
        physics_abs_errors = np.abs(physics_errors)

        bigru_sorted = np.sort(bigru_abs_errors)
        physics_sorted = np.sort(physics_abs_errors)

        percentiles = np.linspace(0, 100, len(bigru_sorted))

        axes[1, 0].plot(percentiles, bigru_sorted, 'r-', linewidth=2, label='BiGRU Model')
        axes[1, 0].plot(percentiles, physics_sorted, 'b-', linewidth=2, label='Physics Model')
        axes[1, 0].set_xlabel('Percentile', fontweight='bold')
        axes[1, 0].set_ylabel('Absolute Error (m/s)', fontweight='bold')
        axes[1, 0].set_title('Cumulative Absolute Error Distribution', fontweight='bold')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)

        # Plot 5: VP/VS ratio analysis
        vp_vs_ratio_real = vp_clean / targets_clean
        vp_vs_ratio_bigru = vp_clean / bigru_clean
        vp_vs_ratio_physics = vp_clean / physics_clean

        axes[1, 1].scatter(vp_clean, vp_vs_ratio_real, alpha=0.5, s=1, c='black', label='Real Data')
        axes[1, 1].scatter(vp_clean, vp_vs_ratio_bigru, alpha=0.5, s=1, c='red', label='BiGRU Model')
        axes[1, 1].scatter(vp_clean, vp_vs_ratio_physics, alpha=0.5, s=1, c='blue', label='Physics Model')

        # Add theoretical mudrock line ratio
        mudrock_ratio = physics_results.get('mudrock_equation', 'VP = 1.16 × VS + 1.36')
        if 'VP = ' in mudrock_ratio:
            # Extract a and b from equation like "VP = 1.16 × VS + 1.36"
            parts = mudrock_ratio.split('×')
            if len(parts) >= 2:
                a_val = float(parts[0].split('=')[1].strip())
                b_val = float(parts[1].split('+')[1].strip())
                theoretical_ratio = a_val + b_val / vp_clean
                axes[1, 1].plot(vp_clean, theoretical_ratio, 'g--', linewidth=2, alpha=0.8, label='Mudrock Line Theory')

        axes[1, 1].set_xlabel('P-wave Velocity (m/s)', fontweight='bold')
        axes[1, 1].set_ylabel('VP/VS Ratio', fontweight='bold')
        axes[1, 1].set_title('VP/VS Ratio Analysis', fontweight='bold')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)

        # Plot 6: Detailed statistics
        axes[1, 2].axis('off')

        # Calculate detailed statistics
        bigru_rmse = np.sqrt(np.mean(bigru_errors ** 2))
        physics_rmse = np.sqrt(np.mean(physics_errors ** 2))

        bigru_mae = np.mean(np.abs(bigru_errors))
        physics_mae = np.mean(np.abs(physics_errors))

        bigru_std = np.std(bigru_errors)
        physics_std = np.std(physics_errors)

        bigru_mean_error = np.mean(bigru_errors)
        physics_mean_error = np.mean(physics_errors)

        # Percentile errors
        bigru_p95 = np.percentile(bigru_abs_errors, 95)
        physics_p95 = np.percentile(physics_abs_errors, 95)

        stats_text = f'📊 DETAILED STATISTICS\n'
        stats_text += f'{"="*30}\n\n'

        stats_text += f'🤖 BiGRU Model Errors:\n'
        stats_text += f'  RMSE: {bigru_rmse:.3f} m/s\n'
        stats_text += f'  MAE:  {bigru_mae:.3f} m/s\n'
        stats_text += f'  Mean: {bigru_mean_error:.3f} m/s\n'
        stats_text += f'  Std:  {bigru_std:.3f} m/s\n'
        stats_text += f'  95th percentile: {bigru_p95:.3f} m/s\n\n'

        stats_text += f'⚗️ Physics Model Errors:\n'
        stats_text += f'  RMSE: {physics_rmse:.3f} m/s\n'
        stats_text += f'  MAE:  {physics_mae:.3f} m/s\n'
        stats_text += f'  Mean: {physics_mean_error:.3f} m/s\n'
        stats_text += f'  Std:  {physics_std:.3f} m/s\n'
        stats_text += f'  95th percentile: {physics_p95:.3f} m/s\n\n'

        stats_text += f'📈 VP/VS Ratio Statistics:\n'
        stats_text += f'  Real data mean: {np.mean(vp_vs_ratio_real):.3f}\n'
        stats_text += f'  BiGRU mean: {np.mean(vp_vs_ratio_bigru):.3f}\n'
        stats_text += f'  Physics mean: {np.mean(vp_vs_ratio_physics):.3f}\n\n'

        stats_text += f'🎯 Model Comparison:\n'
        improvement = ((physics_rmse - bigru_rmse) / physics_rmse) * 100
        if improvement > 0:
            stats_text += f'  BiGRU improves RMSE by {improvement:.1f}%\n'
        else:
            stats_text += f'  Physics model better by {-improvement:.1f}%\n'

        axes[1, 2].text(0.05, 0.95, stats_text, transform=axes[1, 2].transAxes,
                       verticalalignment='top', fontsize=9, fontfamily='monospace',
                       bbox=dict(boxstyle='round,pad=0.5', facecolor='lightyellow', alpha=0.9, edgecolor='orange'))

        plt.tight_layout()

        # Save plot
        physics_detailed_path = self.output_dir / "visualizations" / "physics_detailed_analysis.png"
        plt.savefig(physics_detailed_path, dpi=300, bbox_inches='tight')
        print(f"    Physics detailed analysis plot saved to '{physics_detailed_path}'")
        plt.close(fig)

    def _create_well_specific_visualizations(self, data_splits, model, preprocessor, evaluation_results):
        """Create separate visualizations for each well, sorted by well name."""
        try:
            print("  Creating well-specific visualizations sorted by well name...")

            if not hasattr(self, 'well_data_individual') or not self.well_data_individual:
                print("  Warning: No individual well data available for well-specific plots")
                return

            # Get sorted well names
            sorted_well_names = sorted(self.well_data_individual.keys())
            print(f"  Processing {len(sorted_well_names)} wells in sorted order:")
            for well_name in sorted_well_names:
                print(f"    - {well_name}")

            # Create individual plots for each well
            self._create_individual_well_plots(sorted_well_names, model, preprocessor)

            # Create comparison plot showing all wells
            self._create_wells_comparison_plot(sorted_well_names, model, preprocessor)

            print("  Well-specific visualizations completed successfully!")

        except Exception as e:
            print(f"  Error creating well-specific visualizations: {e}")
            import traceback
            traceback.print_exc()

    def _create_individual_well_plots(self, sorted_well_names, model, preprocessor):
        """Create individual plots for each well."""
        print("    Creating individual well plots...")

        for well_name in sorted_well_names:
            try:
                well_data = self.well_data_individual[well_name]

                # Process well data through the same pipeline
                features = well_data['features']
                targets = well_data['targets']

                # Validation: Ensure this is truly individual well data
                print(f"      {well_name}: Processing {len(features):,} raw data points from depth {well_data['depth_range'][0]:.0f}-{well_data['depth_range'][1]:.0f}m")

                # Create sequences for this well
                processor = WellLogDataProcessor()
                seq_features, seq_targets = processor.create_sequences(
                    features, targets,
                    sequence_length=self.config['data']['sequence_length'],
                    stride=self.config['data']['sequence_stride']
                )

                if len(seq_features) == 0:
                    print(f"      {well_name}: No sequences could be created, skipping...")
                    continue

                # Preprocess
                seq_features_norm, seq_targets_norm = preprocessor.transform(
                    seq_features, seq_targets
                )

                # Make predictions
                model.eval()
                with torch.no_grad():
                    features_tensor = torch.FloatTensor(seq_features_norm)
                    if torch.cuda.is_available() and next(model.parameters()).is_cuda:
                        features_tensor = features_tensor.cuda()
                    predictions = model(features_tensor).cpu().numpy()

                # Inverse transform
                predictions_orig = preprocessor.inverse_transform_targets(predictions)
                targets_orig = preprocessor.inverse_transform_targets(seq_targets_norm)

                # Create plot for this well
                self._plot_single_well(well_name, well_data, seq_features_norm,
                                     targets_orig, predictions_orig, preprocessor)

                print(f"      {well_name}: Plot created successfully")

            except Exception as e:
                print(f"      {well_name}: Error creating plot - {e}")

    def _plot_single_well(self, well_name, well_data, features, targets_orig, predictions_orig, preprocessor):
        """Create a comprehensive plot for a single well."""
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))

        # Enhanced title with well name prominently displayed
        clean_well_name = well_name.replace('.las', '').replace('_', ' ')
        fig.suptitle(f'Individual Well Analysis: {clean_well_name}',
                    fontsize=18, fontweight='bold', color='darkblue')

        # Flatten data for plotting
        targets_flat = targets_orig.reshape(-1)
        predictions_flat = predictions_orig.reshape(-1)

        # Remove invalid values
        valid_mask = (
            ~np.isnan(targets_flat) &
            ~np.isnan(predictions_flat) &
            ~np.isinf(targets_flat) &
            ~np.isinf(predictions_flat)
        )

        targets_clean = targets_flat[valid_mask]
        predictions_clean = predictions_flat[valid_mask]

        if len(targets_clean) == 0:
            print(f"      Warning: No valid data for {well_name}")
            # Create a placeholder plot indicating no data
            for ax in axes.flat:
                ax.text(0.5, 0.5, f'No Valid Data Available\nfor {clean_well_name}',
                       ha='center', va='center', transform=ax.transAxes,
                       fontsize=14, bbox=dict(boxstyle='round', facecolor='lightcoral', alpha=0.8))
                ax.set_title('No Data')
            plt.tight_layout()
            safe_well_name = well_name.replace('.', '_').replace(' ', '_')
            well_plot_path = self.output_dir / "visualizations" / f"well_specific_{safe_well_name}.png"
            plt.savefig(well_plot_path, dpi=300, bbox_inches='tight')
            plt.close(fig)
            return

        print(f"      Processing {len(targets_clean):,} data points for {well_name}")

        # Plot 1: Predictions vs Actual
        axes[0, 0].scatter(targets_clean, predictions_clean, alpha=0.7, s=3, c='blue', edgecolors='navy', linewidth=0.1)
        min_val, max_val = targets_clean.min(), targets_clean.max()
        axes[0, 0].plot([min_val, max_val], [min_val, max_val], 'r--', lw=2, alpha=0.8, label='Perfect Prediction')
        axes[0, 0].set_xlabel('Actual S-wave Velocity (m/s)', fontweight='bold')
        axes[0, 0].set_ylabel('Predicted S-wave Velocity (m/s)', fontweight='bold')

        # Calculate metrics
        rmse = np.sqrt(np.mean((predictions_clean - targets_clean) ** 2))
        correlation = np.corrcoef(targets_clean, predictions_clean)[0, 1]
        r2 = correlation ** 2
        mae = np.mean(np.abs(predictions_clean - targets_clean))

        axes[0, 0].set_title(f'{clean_well_name} - Predictions vs Actual\nRMSE: {rmse:.3f} m/s | R²: {r2:.3f} | Correlation: {correlation:.3f}',
                           fontweight='bold')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

        # Plot 2: Well log display
        depth_indices = np.arange(len(targets_clean)) * 0.5
        depth_start = well_data['depth_range'][0]
        actual_depths = depth_start + depth_indices

        axes[0, 1].plot(targets_clean, actual_depths, 'b-', linewidth=2, alpha=0.8, label='Actual S-wave')
        axes[0, 1].plot(predictions_clean, actual_depths, 'r--', linewidth=2, alpha=0.8, label='Predicted S-wave')
        axes[0, 1].set_xlabel('S-wave Velocity (m/s)', fontweight='bold')
        axes[0, 1].set_ylabel('Depth (m)', fontweight='bold')

        depth_range_text = f"{well_data['depth_range'][0]:.0f}-{well_data['depth_range'][1]:.0f}m"
        axes[0, 1].set_title(f'{clean_well_name} - Well Log Display\nDepth Range: {depth_range_text} | {len(targets_clean):,} points',
                           fontweight='bold')
        axes[0, 1].legend(loc='best')
        axes[0, 1].grid(True, alpha=0.3)
        axes[0, 1].invert_yaxis()

        # Add depth range annotation
        axes[0, 1].text(0.02, 0.98, f'Well: {clean_well_name}', transform=axes[0, 1].transAxes,
                       verticalalignment='top', fontweight='bold',
                       bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.7))

        # Plot 3: Residuals
        residuals = predictions_clean - targets_clean
        axes[1, 0].scatter(targets_clean, residuals, alpha=0.7, s=3, c='green', edgecolors='darkgreen', linewidth=0.1)
        axes[1, 0].axhline(y=0, color='r', linestyle='--', alpha=0.8, linewidth=2, label='Zero Error')
        axes[1, 0].set_xlabel('Actual S-wave Velocity (m/s)', fontweight='bold')
        axes[1, 0].set_ylabel('Residuals (Predicted - Actual) (m/s)', fontweight='bold')

        mean_residual = np.mean(residuals)
        std_residual = np.std(residuals)
        axes[1, 0].set_title(f'{clean_well_name} - Residual Analysis\nMean: {mean_residual:.3f} m/s | Std: {std_residual:.3f} m/s | MAE: {mae:.3f} m/s',
                           fontweight='bold')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)

        # Add residual statistics
        axes[1, 0].axhline(y=mean_residual, color='orange', linestyle=':', alpha=0.7, label=f'Mean: {mean_residual:.3f}')
        axes[1, 0].axhline(y=mean_residual + std_residual, color='orange', linestyle=':', alpha=0.5)
        axes[1, 0].axhline(y=mean_residual - std_residual, color='orange', linestyle=':', alpha=0.5)

        # Plot 4: Statistics and info
        axes[1, 1].axis('off')

        # Well information header
        well_info_text = f'🏗️ WELL INFORMATION\n'
        well_info_text += f'{"="*25}\n'
        well_info_text += f'Well Name: {clean_well_name}\n'
        well_info_text += f'File: {os.path.basename(well_data["file_path"])}\n'
        well_info_text += f'Depth Range: {well_data["depth_range"][0]:.0f} - {well_data["depth_range"][1]:.0f} m\n'
        well_info_text += f'Total Depth: {well_data["depth_range"][1] - well_data["depth_range"][0]:.0f} m\n'
        well_info_text += f'Data Points: {len(targets_clean):,}\n\n'

        # Performance metrics
        stats_text = f'📊 PERFORMANCE METRICS\n'
        stats_text += f'{"="*25}\n'
        stats_text += f'RMSE: {rmse:.4f} m/s\n'
        stats_text += f'R² Score: {r2:.4f}\n'
        stats_text += f'Correlation: {correlation:.4f}\n'
        stats_text += f'MAE: {mae:.4f} m/s\n'
        stats_text += f'Mean Residual: {mean_residual:.4f} m/s\n'
        stats_text += f'Std Residual: {std_residual:.4f} m/s\n\n'

        # Data ranges
        range_text = f'📈 DATA RANGES\n'
        range_text += f'{"="*25}\n'
        range_text += f'Actual S-wave:\n'
        range_text += f'  Min: {targets_clean.min():.0f} m/s\n'
        range_text += f'  Max: {targets_clean.max():.0f} m/s\n'
        range_text += f'  Mean: {np.mean(targets_clean):.0f} m/s\n'
        range_text += f'Predicted S-wave:\n'
        range_text += f'  Min: {predictions_clean.min():.0f} m/s\n'
        range_text += f'  Max: {predictions_clean.max():.0f} m/s\n'
        range_text += f'  Mean: {np.mean(predictions_clean):.0f} m/s\n'

        full_text = well_info_text + stats_text + range_text

        axes[1, 1].text(0.05, 0.95, full_text, transform=axes[1, 1].transAxes,
                       verticalalignment='top', fontsize=9, fontfamily='monospace',
                       bbox=dict(boxstyle='round,pad=0.5', facecolor='lightblue', alpha=0.9, edgecolor='navy'))

        plt.tight_layout()

        # Save plot
        safe_well_name = well_name.replace('.', '_').replace(' ', '_')
        well_plot_path = self.output_dir / "visualizations" / f"well_specific_{safe_well_name}.png"
        plt.savefig(well_plot_path, dpi=300, bbox_inches='tight')
        plt.close(fig)

    def _create_wells_comparison_plot(self, sorted_well_names, model, preprocessor):
        """Create a comparison plot showing all wells side by side."""
        print("    Creating wells comparison plot...")

        try:
            # Calculate number of rows and columns for subplots
            n_wells = len(sorted_well_names)
            n_cols = min(3, n_wells)  # Maximum 3 columns
            n_rows = (n_wells + n_cols - 1) // n_cols  # Ceiling division

            fig, axes = plt.subplots(n_rows, n_cols, figsize=(6*n_cols, 8*n_rows))
            fig.suptitle('Well Comparison - S-wave Velocity Predictions (Sorted by Well Name)',
                        fontsize=16, fontweight='bold')

            # Normalize axes to a flat 1D array for consistent indexing
            axes = np.atleast_1d(axes).ravel()

            well_metrics = {}

            for idx, well_name in enumerate(sorted_well_names):
                ax = axes[idx]

                try:
                    well_data = self.well_data_individual[well_name]

                    # Process well data
                    features = well_data['features']
                    targets = well_data['targets']

                    # Create sequences
                    processor = WellLogDataProcessor()
                    seq_features, seq_targets = processor.create_sequences(
                        features, targets,
                        sequence_length=self.config['data']['sequence_length'],
                        stride=self.config['data']['sequence_stride']
                    )

                    if len(seq_features) == 0:
                        ax.text(0.5, 0.5, f'{well_name}\n(No sequences)',
                               ha='center', va='center', transform=ax.transAxes)
                        ax.set_title(well_name)
                        continue

                    # Preprocess and predict
                    seq_features_norm, seq_targets_norm = preprocessor.transform(seq_features, seq_targets)

                    model.eval()
                    with torch.no_grad():
                        features_tensor = torch.FloatTensor(seq_features_norm)
                        if torch.cuda.is_available() and next(model.parameters()).is_cuda:
                            features_tensor = features_tensor.cuda()
                        predictions = model(features_tensor).cpu().numpy()

                    # Inverse transform
                    predictions_orig = preprocessor.inverse_transform_targets(predictions)
                    targets_orig = preprocessor.inverse_transform_targets(seq_targets_norm)

                    # Flatten and clean data
                    targets_flat = targets_orig.reshape(-1)
                    predictions_flat = predictions_orig.reshape(-1)

                    valid_mask = (
                        ~np.isnan(targets_flat) & ~np.isnan(predictions_flat) &
                        ~np.isinf(targets_flat) & ~np.isinf(predictions_flat)
                    )

                    targets_clean = targets_flat[valid_mask]
                    predictions_clean = predictions_flat[valid_mask]

                    if len(targets_clean) == 0:
                        ax.text(0.5, 0.5, f'{well_name}\n(No valid data)',
                               ha='center', va='center', transform=ax.transAxes)
                        ax.set_title(well_name)
                        continue

                    # Create well log style plot
                    depth_indices = np.arange(len(targets_clean)) * 0.5
                    depth_start = well_data['depth_range'][0]
                    actual_depths = depth_start + depth_indices

                    ax.plot(targets_clean, actual_depths, 'b-', linewidth=2, alpha=0.8, label='Actual')
                    ax.plot(predictions_clean, actual_depths, 'r--', linewidth=2, alpha=0.8, label='Predicted')

                    # Calculate metrics
                    rmse = np.sqrt(np.mean((predictions_clean - targets_clean) ** 2))
                    correlation = np.corrcoef(targets_clean, predictions_clean)[0, 1]
                    r2 = correlation ** 2

                    well_metrics[well_name] = {'rmse': rmse, 'r2': r2, 'correlation': correlation, 'n_points': len(targets_clean)}

                    ax.set_xlabel('S-wave Velocity (m/s)', fontweight='bold')
                    ax.set_ylabel('Depth (m)', fontweight='bold')

                    # Enhanced title with clean well name
                    clean_well_name = well_name.replace('.las', '').replace('_', ' ')
                    ax.set_title(f'{clean_well_name}\nRMSE: {rmse:.3f} | R²: {r2:.3f}\n{len(targets_clean):,} points',
                               fontweight='bold', fontsize=10)
                    ax.legend(fontsize=8, loc='best')
                    ax.grid(True, alpha=0.3)
                    ax.invert_yaxis()

                    # Add well name as text annotation for extra clarity
                    ax.text(0.02, 0.02, clean_well_name, transform=ax.transAxes,
                           fontweight='bold', fontsize=8,
                           bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7))

                except Exception as e:
                    clean_well_name = well_name.replace('.las', '').replace('_', ' ')
                    ax.text(0.5, 0.5, f'{clean_well_name}\n(Error: {str(e)[:30]}...)',
                           ha='center', va='center', transform=ax.transAxes,
                           bbox=dict(boxstyle='round', facecolor='lightcoral', alpha=0.8))
                    ax.set_title(clean_well_name, fontweight='bold')
                    print(f"      Error processing {well_name}: {e}")

            # Hide empty subplots
            for idx in range(n_wells, n_rows * n_cols):
                axes[idx].set_visible(False)

            plt.tight_layout()

            # Save comparison plot
            comparison_path = self.output_dir / "visualizations" / "wells_comparison_sorted.png"
            plt.savefig(comparison_path, dpi=300, bbox_inches='tight')
            print(f"    Wells comparison plot saved to '{comparison_path}'")
            plt.close(fig)

            # Print summary of well metrics
            print(f"    Well Performance Summary (sorted by name):")
            print(f"    {'Well Name':<25} {'RMSE':<8} {'R²':<8} {'Corr':<8} {'Points':<10}")
            print(f"    {'-'*65}")
            for well_name in sorted_well_names:
                clean_name = well_name.replace('.las', '').replace('_', ' ')
                if well_name in well_metrics:
                    metrics = well_metrics[well_name]
                    print(f"    {clean_name:<25} {metrics['rmse']:<8.3f} {metrics['r2']:<8.3f} {metrics['correlation']:<8.3f} {metrics['n_points']:<10,}")
                else:
                    print(f"    {clean_name:<25} {'No valid data':<35}")

        except Exception as e:
            print(f"    Error creating wells comparison plot: {e}")
            import traceback
            traceback.print_exc()

    def _print_summary(self):
        """Print summary of results."""
        print("\nPIPELINE SUMMARY:")
        print("-" * 50)

        # Well information summary
        if self.well_info:
            print(f"📁 WELL INFORMATION:")
            usable_wells = sum(1 for info in self.well_info.values() if info.get('usable_for_training', False))
            print(f"  • Total wells analyzed: {len(self.well_info)}")
            print(f"  • Usable for training: {usable_wells}")
            print(f"  • Average data quality: {np.mean([info.get('data_quality_score', 0) for info in self.well_info.values()]):.1f}%")

            print(f"\n  Well Details:")
            for well_name, info in self.well_info.items():
                if info.get('usable_for_training', False):
                    depth_range = info.get('depth_range', (0, 0))
                    quality = info.get('data_quality_score', 0)
                    print(f"    ✓ {well_name}: {depth_range[0]:.0f}-{depth_range[1]:.0f}m, Quality: {quality:.1f}%")
                else:
                    print(f"    ✗ {well_name}: Not usable - {info.get('error', 'Missing required curves')}")

        # Data processing summary
        if 'data_loading' in self.results:
            print(f"\n📊 DATA PROCESSING:")
            print(f"  • LAS files processed: {len(self.results['data_loading'])}")

        if 'data_processing' in self.results:
            proc_results = self.results['data_processing']
            print(f"  • Combined feature shape: {proc_results['feature_shape']}")
            print(f"  • Combined target shape: {proc_results['target_shape']}")
            print(f"  • Successfully processed files: {proc_results['successful_files']}")

        if 'sequence_creation' in self.results:
            seq_results = self.results['sequence_creation']
            print(f"  • Sequence features shape: {seq_results['sequence_features_shape']}")
            print(f"  • Sequence targets shape: {seq_results['sequence_targets_shape']}")

        # Data split summary
        if self.well_usage:
            print(f"\n🔄 DATA SPLITS:")
            for split_name, split_info in self.well_usage.items():
                print(f"  • {split_name.capitalize()}: {split_info['num_sequences']:,} sequences ({split_info['percentage']:.1f}%)")

        # Training summary
        if 'training' in self.results:
            train_results = self.results['training']
            print(f"\n🚀 TRAINING:")
            print(f"  • Training epochs: {train_results['final_epoch']}")
            print(f"  • Best validation loss: {train_results['best_val_loss']:.4f}")

        # Model performance summary
        if 'evaluation' in self.results:
            eval_results = self.results['evaluation']
            print(f"\n🎯 MODEL PERFORMANCE:")
            print(f"  • Test RMSE: {eval_results['rmse']:.4f} m/s")
            print(f"  • Test R²: {eval_results['r2']:.4f}")
            print(f"  • Test Correlation: {eval_results['correlation']:.4f}")
            print(f"  • Test MAE: {eval_results['mae']:.4f} m/s")

        # Physics guidance summary
        if 'physics_guidance' in self.results:
            phys_results = self.results['physics_guidance']
            print(f"\n⚗️ PHYSICS GUIDANCE:")
            print(f"  • Physics model RMSE: {phys_results['physics_rmse']:.4f} m/s")
            print(f"  • Physics model correlation: {phys_results['physics_correlation']:.4f}")

        # Output files summary
        print(f"\n📁 OUTPUT FILES (saved to '{self.output_dir}'):")
        print(f"  • General visualizations: 4-5 PNG files with comprehensive analysis")
        print(f"  • Physics comparison plots: 2 PNG files comparing BiGRU vs Physics models")
        if hasattr(self, 'well_data_individual') and self.well_data_individual:
            n_wells = len(self.well_data_individual)
            print(f"  • Well-specific plots: {n_wells} individual well plots + 1 comparison plot")
        print(f"  • Model: PyTorch model file (.pth)")
        print(f"  • Results: JSON file with detailed metrics and well information")

        print("-" * 50)


def main():
    """Main function to run the test."""
    try:
        # Initialize and run the test
        pipeline_test = LASMLPipelineTest()
        results = pipeline_test.run_complete_test()

        # Add well information to results
        results['well_information'] = pipeline_test.well_info
        results['well_usage'] = pipeline_test.well_usage
        results['output_directory'] = str(pipeline_test.output_dir.absolute())
        results['timestamp'] = datetime.now().isoformat()

        # Save results
        print("\nSaving comprehensive results...")
        save_results_to_json(results, pipeline_test.output_dir)

        results_path = pipeline_test.output_dir / "results" / "las_ml_pipeline_results.json"
        print(f"Results saved to '{results_path}'")

        # Print summary of generated files
        print(f"\nGenerated files in '{pipeline_test.output_dir}':")
        print("  📊 Visualizations:")

        # Categorize different types of plots
        viz_files = list((pipeline_test.output_dir / "visualizations").glob("*.png"))
        general_plots = [f for f in viz_files if not f.name.startswith(("well_specific_", "physics_"))]
        physics_plots = [f for f in viz_files if f.name.startswith("physics_")]
        well_plots = [f for f in viz_files if f.name.startswith("well_specific_")]

        print("    General Analysis:")
        for viz_file in sorted(general_plots):
            print(f"      • {viz_file.name}")

        if physics_plots:
            print("    Physics-Informed Analysis:")
            for viz_file in sorted(physics_plots):
                print(f"      • {viz_file.name}")

        if well_plots:
            print("    Well-Specific Analysis (sorted by well name):")
            for viz_file in sorted(well_plots):
                print(f"      • {viz_file.name}")

        print("  🤖 Models:")
        for model_file in (pipeline_test.output_dir / "models").glob("*.pth"):
            print(f"    • {model_file.name}")
        print("  📋 Results:")
        for result_file in (pipeline_test.output_dir / "results").glob("*.json"):
            print(f"    • {result_file.name}")

        return results

    except Exception as e:
        print(f"Error in main execution: {e}")
        import traceback
        traceback.print_exc()
        return None

def save_results_to_json(results, output_dir):
    """Save results to JSON file with proper serialization."""
    def convert_for_json(obj):
        """Convert numpy arrays and other non-serializable objects to JSON-compatible format."""
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, dict):
            return {k: convert_for_json(v) for k, v in obj.items()}
        elif isinstance(obj, (list, tuple)):
            return [convert_for_json(item) for item in obj]
        elif isinstance(obj, Path):
            return str(obj)
        elif hasattr(obj, 'tolist'):
            return obj.tolist()
        else:
            return obj

    json_results = convert_for_json(results)

    results_path = output_dir / "results" / "las_ml_pipeline_results.json"
    with open(results_path, 'w') as f:
        json.dump(json_results, f, indent=2, default=str)


if __name__ == "__main__":
    results = main()