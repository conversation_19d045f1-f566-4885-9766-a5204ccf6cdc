#!/usr/bin/env python3
"""
Diagnostic script to analyze data usage in the LAS ML pipeline.
This script will show exactly how much data is being used at each step.
"""
import os
import sys
import numpy as np

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from data.las_loader import LASLoader, WellLogDataProcessor

def analyze_single_las_file(las_file):
    """Analyze a single LAS file to see data usage."""
    print(f"\n{'='*60}")
    print(f"ANALYZING: {os.path.basename(las_file)}")
    print(f"{'='*60}")
    
    # Step 1: Load raw LAS data
    loader = LASLoader()
    try:
        raw_data = loader.load(las_file)
        print(f"1. Raw LAS data loaded:")
        print(f"   - Shape: {raw_data.shape}")
        print(f"   - Columns: {len(raw_data.columns)}")
        print(f"   - Available curves: {list(raw_data.columns)}")
        
        # Check depth range
        if 'DEPTH' in raw_data.columns:
            depth_col = raw_data['DEPTH']
            print(f"   - Depth range: {depth_col.min():.1f} to {depth_col.max():.1f}")
            print(f"   - Depth increment: {(depth_col.iloc[1] - depth_col.iloc[0]):.1f}")
        
    except Exception as e:
        print(f"   ERROR loading LAS file: {e}")
        return
    
    # Step 2: Check required curves
    required_features = ['P-WAVE', 'RHOB', 'PHIE', 'RT']
    required_targets = ['S-WAVE']
    
    processor = WellLogDataProcessor(
        target_curves=required_targets,
        feature_curves=required_features
    )
    
    # Get curve mapping
    curve_mapping = processor._get_curve_mapping(loader.get_curve_names())
    print(f"\n2. Curve mapping:")
    for standard, actual in curve_mapping.items():
        print(f"   - {standard} -> {actual}")
    
    # Step 3: Extract features and targets
    features = processor._extract_curves(raw_data, required_features, curve_mapping)
    targets = processor._extract_curves(raw_data, required_targets, curve_mapping)
    
    if features is not None and targets is not None:
        print(f"\n3. After curve extraction:")
        print(f"   - Features shape: {features.shape}")
        print(f"   - Targets shape: {targets.shape}")
        
        # Check for missing values
        feature_nan_count = np.isnan(features).sum()
        target_nan_count = np.isnan(targets).sum()
        print(f"   - Features NaN count: {feature_nan_count}")
        print(f"   - Targets NaN count: {target_nan_count}")
        
        # Check percentage of valid data
        total_rows = len(features)
        feature_nan_rows = np.isnan(features).any(axis=1).sum()
        target_nan_rows = np.isnan(targets).any(axis=1).sum()
        combined_nan_rows = np.isnan(np.concatenate([features, targets], axis=1)).any(axis=1).sum()
        
        print(f"   - Rows with any NaN in features: {feature_nan_rows} ({feature_nan_rows/total_rows*100:.1f}%)")
        print(f"   - Rows with any NaN in targets: {target_nan_rows} ({target_nan_rows/total_rows*100:.1f}%)")
        print(f"   - Rows with any NaN (combined): {combined_nan_rows} ({combined_nan_rows/total_rows*100:.1f}%)")
        
        # Step 4: After cleaning
        combined_data = np.concatenate([features, targets], axis=1)
        valid_mask = ~np.isnan(combined_data).any(axis=1)
        valid_rows = np.sum(valid_mask)
        
        print(f"\n4. After data cleaning:")
        print(f"   - Valid rows: {valid_rows} ({valid_rows/total_rows*100:.1f}%)")
        print(f"   - Removed rows: {total_rows - valid_rows} ({(total_rows - valid_rows)/total_rows*100:.1f}%)")
        
        if valid_rows > 0:
            features_clean = features[valid_mask]
            targets_clean = targets[valid_mask]
            
            # Show data statistics
            print(f"\n5. Clean data statistics:")
            print(f"   - Features shape: {features_clean.shape}")
            print(f"   - Targets shape: {targets_clean.shape}")
            
            # Show feature ranges
            feature_names = required_features
            for i, name in enumerate(feature_names):
                if i < features_clean.shape[1]:
                    col_data = features_clean[:, i]
                    print(f"   - {name}: {col_data.min():.2f} to {col_data.max():.2f} (mean: {col_data.mean():.2f})")
            
            # Show target ranges
            target_names = required_targets
            for i, name in enumerate(target_names):
                if i < targets_clean.shape[1]:
                    col_data = targets_clean[:, i]
                    print(f"   - {name}: {col_data.min():.2f} to {col_data.max():.2f} (mean: {col_data.mean():.2f})")
            
            return features_clean, targets_clean
        else:
            print("   - No valid data remaining after cleaning!")
            return None, None
    else:
        print("\n3. ERROR: Could not extract required curves")
        return None, None

def analyze_sequence_creation(features, targets, sequence_length=50, stride=5):
    """Analyze sequence creation step."""
    if features is None or targets is None:
        return
    
    print(f"\n{'='*60}")
    print(f"SEQUENCE CREATION ANALYSIS")
    print(f"{'='*60}")
    
    print(f"Input data:")
    print(f"   - Features shape: {features.shape}")
    print(f"   - Targets shape: {targets.shape}")
    print(f"   - Sequence length: {sequence_length}")
    print(f"   - Stride: {stride}")
    
    if len(features) < sequence_length:
        print(f"   - WARNING: Not enough data for sequences!")
        print(f"   - Available: {len(features)}, Required: {sequence_length}")
        return
    
    # Calculate number of sequences
    n_sequences = (len(features) - sequence_length) // stride + 1
    print(f"   - Number of sequences: {n_sequences}")
    print(f"   - Data utilization: {(n_sequences * sequence_length) / len(features) * 100:.1f}%")
    
    # Create sequences
    processor = WellLogDataProcessor()
    seq_features, seq_targets = processor.create_sequences(
        features, targets, sequence_length, stride
    )
    
    print(f"\nSequence output:")
    print(f"   - Sequence features shape: {seq_features.shape}")
    print(f"   - Sequence targets shape: {seq_targets.shape}")

def main():
    """Main diagnostic function."""
    print("LAS Data Usage Diagnostic")
    print("=" * 60)
    
    # Find LAS files
    las_dir = "Las"
    if not os.path.exists(las_dir):
        print(f"ERROR: LAS directory not found: {las_dir}")
        return
    
    las_files = [os.path.join(las_dir, f) for f in os.listdir(las_dir) if f.lower().endswith('.las')]
    
    if not las_files:
        print(f"ERROR: No LAS files found in {las_dir}")
        return
    
    print(f"Found {len(las_files)} LAS files")
    
    all_features = []
    all_targets = []
    
    # Analyze each file
    for las_file in las_files:
        features, targets = analyze_single_las_file(las_file)
        if features is not None and targets is not None:
            all_features.append(features)
            all_targets.append(targets)
    
    # Combine all data
    if all_features:
        print(f"\n{'='*60}")
        print(f"COMBINED DATA ANALYSIS")
        print(f"{'='*60}")
        
        combined_features = np.concatenate(all_features, axis=0)
        combined_targets = np.concatenate(all_targets, axis=0)
        
        print(f"Combined data:")
        print(f"   - Features shape: {combined_features.shape}")
        print(f"   - Targets shape: {combined_targets.shape}")
        print(f"   - Total data points: {len(combined_features)}")
        
        # Analyze sequence creation with different parameters
        sequence_configs = [
            (50, 5),    # Old default
            (100, 10),  # New default - longer sequences, better utilization
            (200, 20),  # Even longer sequences
            (50, 1),    # Short sequences, maximum overlap
        ]
        
        for seq_len, stride in sequence_configs:
            print(f"\n--- Sequence Config: length={seq_len}, stride={stride} ---")
            analyze_sequence_creation(combined_features, combined_targets, seq_len, stride)
    
    else:
        print("\nERROR: No valid data could be extracted from any LAS files")

if __name__ == "__main__":
    main()
