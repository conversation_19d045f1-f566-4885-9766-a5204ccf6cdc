from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
import numpy as np

class RockPhysicsModel(ABC):
    """Abstract base class for rock physics models."""

    def __init__(self, name: str, **kwargs):
        self.name = name
        self.params = kwargs
        self._fitted = False

    @abstractmethod
    def predict(self, vp: np.ndarray, **kwargs) -> np.ndarray:
        """Predict S-wave velocity from P-wave velocity and other parameters."""
        pass

    @abstractmethod
    def fit(self, vp: np.ndarray, vs: np.ndarray, **kwargs) -> None:
        """Fit model parameters from training data."""
        pass

    @property
    def fitted(self) -> bool:
        return self._fitted

    def get_params(self) -> Dict[str, Any]:
        """Return model parameters."""
        return self.params

    def set_params(self, **params) -> None:
        """Set model parameters."""
        self.params.update(params)
