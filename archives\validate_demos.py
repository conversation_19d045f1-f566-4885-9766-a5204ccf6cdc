#!/usr/bin/env python3
"""
Validation script to check if the demo scripts can run successfully.
This performs basic validation without running the full training pipeline.
"""
import os
import sys
import importlib.util

def check_file_exists(filepath, description):
    """Check if a file exists and print status."""
    if os.path.exists(filepath):
        size = os.path.getsize(filepath) / 1024  # KB
        print(f"  ✓ {description}: {filepath} ({size:.1f} KB)")
        return True
    else:
        print(f"  ✗ {description}: {filepath} (NOT FOUND)")
        return False

def check_import(module_path, module_name):
    """Check if a module can be imported."""
    try:
        spec = importlib.util.spec_from_file_location(module_name, module_path)
        module = importlib.util.module_from_spec(spec)
        sys.modules[module_name] = module
        spec.loader.exec_module(module)
        print(f"  ✓ {module_name} imports successfully")
        return True
    except Exception as e:
        print(f"  ✗ {module_name} import failed: {e}")
        return False

def validate_project_structure():
    """Validate the basic project structure."""
    print("1. Validating Project Structure")
    print("-" * 40)
    
    required_files = [
        ("README.md", "Project documentation"),
        ("requirements.txt", "Python dependencies"),
        ("run_las_demo.py", "Quick demo script"),
        ("test_las_ml_pipeline.py", "Full pipeline test"),
        ("configs/default_config.yaml", "Default configuration"),
    ]
    
    required_dirs = [
        ("src", "Source code directory"),
        ("src/data", "Data processing modules"),
        ("src/models", "Model definitions"),
        ("src/training", "Training infrastructure"),
        ("Las", "Sample LAS files"),
        ("tests", "Unit tests"),
    ]
    
    all_good = True
    
    for filepath, description in required_files:
        if not check_file_exists(filepath, description):
            all_good = False
    
    for dirpath, description in required_dirs:
        if os.path.exists(dirpath) and os.path.isdir(dirpath):
            file_count = len([f for f in os.listdir(dirpath) if os.path.isfile(os.path.join(dirpath, f))])
            print(f"  ✓ {description}: {dirpath} ({file_count} files)")
        else:
            print(f"  ✗ {description}: {dirpath} (NOT FOUND)")
            all_good = False
    
    return all_good

def validate_dependencies():
    """Validate that required Python packages are available."""
    print("\n2. Validating Dependencies")
    print("-" * 40)
    
    required_packages = [
        'torch', 'numpy', 'matplotlib', 'pandas', 'sklearn', 
        'yaml', 'tqdm', 'seaborn'
    ]
    
    all_good = True
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✓ {package}")
        except ImportError:
            print(f"  ✗ {package} (NOT INSTALLED)")
            all_good = False
    
    return all_good

def validate_las_files():
    """Validate LAS files are present and readable."""
    print("\n3. Validating LAS Files")
    print("-" * 40)
    
    las_dir = "Las"
    if not os.path.exists(las_dir):
        print(f"  ✗ LAS directory not found: {las_dir}")
        return False
    
    las_files = [f for f in os.listdir(las_dir) if f.lower().endswith('.las')]
    
    if not las_files:
        print(f"  ✗ No LAS files found in {las_dir}")
        return False
    
    print(f"  ✓ Found {len(las_files)} LAS files:")
    for las_file in las_files:
        filepath = os.path.join(las_dir, las_file)
        size = os.path.getsize(filepath) / 1024  # KB
        print(f"    - {las_file} ({size:.1f} KB)")
    
    return True

def validate_source_imports():
    """Validate that source modules can be imported."""
    print("\n4. Validating Source Code Imports")
    print("-" * 40)
    
    # Add src to path
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
    
    modules_to_test = [
        ('src/data/las_loader.py', 'data.las_loader'),
        ('src/data/preprocessing.py', 'data.preprocessing'),
        ('src/models/neural_networks.py', 'models.neural_networks'),
        ('src/models/rock_physics/__init__.py', 'models.rock_physics'),
        ('src/training/trainer.py', 'training.trainer'),
        ('src/training/strategies.py', 'training.strategies'),
    ]
    
    all_good = True
    
    for module_path, module_name in modules_to_test:
        if not check_import(module_path, module_name):
            all_good = False
    
    return all_good

def validate_demo_scripts():
    """Validate that demo scripts can be imported."""
    print("\n5. Validating Demo Scripts")
    print("-" * 40)
    
    demo_scripts = [
        ('run_las_demo.py', 'run_las_demo'),
        ('test_las_ml_pipeline.py', 'test_las_ml_pipeline'),
    ]
    
    all_good = True
    
    for script_path, script_name in demo_scripts:
        if not check_import(script_path, script_name):
            all_good = False
    
    return all_good

def main():
    """Main validation function."""
    print("Physics-Guided ML Framework Validation")
    print("=" * 50)
    
    validations = [
        validate_project_structure,
        validate_dependencies,
        validate_las_files,
        validate_source_imports,
        validate_demo_scripts,
    ]
    
    results = []
    for validation_func in validations:
        try:
            result = validation_func()
            results.append(result)
        except Exception as e:
            print(f"  ✗ Validation failed with error: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print("VALIDATION SUMMARY")
    print("=" * 50)
    
    validation_names = [
        "Project Structure",
        "Dependencies", 
        "LAS Files",
        "Source Imports",
        "Demo Scripts"
    ]
    
    all_passed = True
    for i, (name, result) in enumerate(zip(validation_names, results)):
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{i+1}. {name}: {status}")
        if not result:
            all_passed = False
    
    print("-" * 50)
    if all_passed:
        print("🎉 All validations passed! The demo scripts should work correctly.")
        print("\nYou can now run:")
        print("  python run_las_demo.py")
        print("  python test_las_ml_pipeline.py")
        return 0
    else:
        print("❌ Some validations failed. Please fix the issues above.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
