import pytest
import numpy as np
from src.models.rock_physics.mudrock_line import <PERSON><PERSON><PERSON><PERSON><PERSON>

def test_mudrock_line_prediction():
    """Test mudrock line predictions."""
    model = MudrockLine()
    vp = np.array([3.0, 3.5, 4.0])
    vs = model.predict(vp)

    # Check output shape
    assert vs.shape == vp.shape

    # Check approximate values
    expected_vs = (vp - 1.36) / 1.16
    np.testing.assert_allclose(vs, expected_vs, rtol=1e-5)

def test_mudrock_line_fitting():
    """Test mudrock line fitting capability."""
    model = MudrockLine()
    vp_train = np.array([2.5, 3.0, 3.5, 4.0])
    vs_train = np.array([1.0, 1.4, 1.8, 2.2]) # Corrected values

    model.fit(vp_train, vs_train)
    assert model.fitted
    assert model.a != 1.16  # Should have updated from default
    assert model.b != 1.36  # Should have updated from default

    # Test prediction with new coefficients
    vp_test = np.array([3.2])
    vs_pred = model.predict(vp_test)
    assert isinstance(vs_pred, np.ndarray)
    assert vs_pred.shape == (1,)
