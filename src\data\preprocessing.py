"""
Data preprocessing utilities for well log data.
"""
import numpy as np
import pandas as pd
from sklearn.preprocessing import StandardScaler, MinMaxScaler, RobustScaler
from typing import Tuple, Optional, Dict, Any
import joblib
import os


class WellLogPreprocessor:
    """
    Preprocessor for well log data with normalization and feature engineering.
    """

    def __init__(self, normalization: str = 'minmax', feature_engineering: bool = True):
        """
        Initialize preprocessor.

        Args:
            normalization: Type of normalization ('minmax', 'standard', 'robust')
            feature_engineering: Whether to apply feature engineering
        """
        self.normalization = normalization
        self.feature_engineering = feature_engineering
        self.scalers = {}
        self.feature_names = []
        self.target_names = []
        self.fitted = False

    def fit(self, features: np.ndarray, targets: np.ndarray,
            feature_names: list = None, target_names: list = None) -> 'WellLogPreprocessor':
        """
        Fit the preprocessor to the training data.

        Args:
            features: Training features
            targets: Training targets
            feature_names: Names of feature columns
            target_names: Names of target columns

        Returns:
            Self for method chaining
        """
        self.feature_names = feature_names or [f'feature_{i}' for i in range(features.shape[1])]
        self.target_names = target_names or [f'target_{i}' for i in range(targets.shape[1])]

        # Create scalers
        scaler_class = self._get_scaler_class()

        # Fit feature scalers
        self.scalers['features'] = scaler_class()
        if len(features.shape) == 3:  # Sequence data
            # Reshape for fitting: (n_samples * seq_len, n_features)
            features_reshaped = features.reshape(-1, features.shape[-1])
            self.scalers['features'].fit(features_reshaped)
        else:
            self.scalers['features'].fit(features)

        # Fit target scalers
        self.scalers['targets'] = scaler_class()
        if len(targets.shape) == 3:  # Sequence data
            # Reshape for fitting: (n_samples * seq_len, n_targets)
            targets_reshaped = targets.reshape(-1, targets.shape[-1])
            self.scalers['targets'].fit(targets_reshaped)
        else:
            self.scalers['targets'].fit(targets)

        self.fitted = True
        return self

    def transform_features(self, features: np.ndarray) -> np.ndarray:
        """
        Transform features using fitted scalers.

        Args:
            features: Features to transform

        Returns:
            Transformed features
        """
        if not self.fitted:
            raise ValueError("Preprocessor must be fitted before transforming")

        original_shape = features.shape

        if len(features.shape) == 3:  # Sequence data
            # Reshape for transformation
            features_reshaped = features.reshape(-1, features.shape[-1])
            transformed = self.scalers['features'].transform(features_reshaped)
            # Reshape back to original shape
            transformed = transformed.reshape(original_shape)
        else:
            transformed = self.scalers['features'].transform(features)

        # Apply feature engineering if enabled
        if self.feature_engineering:
            transformed = self._apply_feature_engineering(transformed, original_shape)

        return transformed

    def transform_targets(self, targets: np.ndarray) -> np.ndarray:
        """
        Transform targets using fitted scalers.

        Args:
            targets: Targets to transform

        Returns:
            Transformed targets
        """
        if not self.fitted:
            raise ValueError("Preprocessor must be fitted before transforming")

        if len(targets.shape) == 3:  # Sequence data
            original_shape = targets.shape
            # Reshape for transformation
            targets_reshaped = targets.reshape(-1, targets.shape[-1])
            transformed = self.scalers['targets'].transform(targets_reshaped)
            # Reshape back to original shape
            transformed = transformed.reshape(original_shape)
        else:
            transformed = self.scalers['targets'].transform(targets)

        return transformed

    def transform(self, features: np.ndarray, targets: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        Transform features and targets together using fitted scalers.

        Args:
            features: Features to transform
            targets: Targets to transform

        Returns:
            Tuple of (transformed_features, transformed_targets)
        """
        if not self.fitted:
            raise ValueError("Preprocessor must be fitted before transforming")
        return self.transform_features(features), self.transform_targets(targets)

    def inverse_transform_targets(self, targets: np.ndarray) -> np.ndarray:
        """
        Inverse transform targets to original scale.

        Args:
            targets: Scaled targets

        Returns:
            Targets in original scale
        """
        if not self.fitted:
            raise ValueError("Preprocessor must be fitted before inverse transforming")

        if len(targets.shape) == 3:  # Sequence data
            original_shape = targets.shape
            # Reshape for inverse transformation
            targets_reshaped = targets.reshape(-1, targets.shape[-1])
            inverse_transformed = self.scalers['targets'].inverse_transform(targets_reshaped)
            # Reshape back to original shape
            inverse_transformed = inverse_transformed.reshape(original_shape)
        else:
            inverse_transformed = self.scalers['targets'].inverse_transform(targets)

        return inverse_transformed

    def fit_transform(self, features: np.ndarray, targets: np.ndarray,
                     feature_names: list = None, target_names: list = None) -> Tuple[np.ndarray, np.ndarray]:
        """
        Fit and transform in one step.

        Args:
            features: Training features
            targets: Training targets
            feature_names: Names of feature columns
            target_names: Names of target columns

        Returns:
            Tuple of (transformed_features, transformed_targets)
        """
        self.fit(features, targets, feature_names, target_names)
        return self.transform_features(features), self.transform_targets(targets)

    def _get_scaler_class(self):
        """Get the appropriate scaler class."""
        if self.normalization == 'minmax':
            return MinMaxScaler
        elif self.normalization == 'standard':
            return StandardScaler
        elif self.normalization == 'robust':
            return RobustScaler
        else:
            raise ValueError(f"Unknown normalization type: {self.normalization}")

    def _apply_feature_engineering(self, features: np.ndarray, original_shape: tuple) -> np.ndarray:
        """
        Apply feature engineering to the features.

        Args:
            features: Normalized features
            original_shape: Original shape of features

        Returns:
            Features with additional engineered features
        """
        if len(original_shape) == 3:  # Sequence data
            n_samples, seq_len, n_features = original_shape
            engineered_features = []

            for i in range(n_samples):
                sample_features = features[i]  # (seq_len, n_features)

                # Add moving averages (window size 3)
                ma_features = self._compute_moving_average(sample_features, window=3)

                # Add feature ratios (if we have VP and VS)
                ratio_features = self._compute_feature_ratios(sample_features)

                # Combine original and engineered features
                combined = np.concatenate([sample_features, ma_features, ratio_features], axis=1)
                engineered_features.append(combined)

            return np.array(engineered_features)
        else:
            # For non-sequence data, apply simpler feature engineering
            ratio_features = self._compute_feature_ratios(features)
            return np.concatenate([features, ratio_features], axis=1)

    def _compute_moving_average(self, features: np.ndarray, window: int = 3) -> np.ndarray:
        """
        Compute moving average features.

        Args:
            features: Input features (seq_len, n_features)
            window: Window size for moving average

        Returns:
            Moving average features
        """
        seq_len, n_features = features.shape
        ma_features = np.zeros_like(features)

        for i in range(seq_len):
            start_idx = max(0, i - window + 1)
            end_idx = i + 1
            ma_features[i] = np.mean(features[start_idx:end_idx], axis=0)

        return ma_features

    def _compute_feature_ratios(self, features: np.ndarray) -> np.ndarray:
        """
        Compute feature ratios (e.g., VP/VS ratio).

        Args:
            features: Input features

        Returns:
            Ratio features
        """
        if features.shape[-1] >= 2:  # Assume first two features are VP and density
            if len(features.shape) == 2:  # Sequence data
                seq_len, _ = features.shape
                ratios = np.zeros((seq_len, 1))
                # Compute VP/density ratio (avoiding division by zero)
                mask = np.abs(features[:, 1]) > 1e-6
                ratios[mask, 0] = features[mask, 0] / features[mask, 1]
            else:  # Non-sequence data
                ratios = np.zeros((features.shape[0], 1))
                mask = np.abs(features[:, 1]) > 1e-6
                ratios[mask, 0] = features[mask, 0] / features[mask, 1]

            return ratios
        else:
            # Return zeros if not enough features for ratios
            if len(features.shape) == 2:
                return np.zeros((features.shape[0], 1))
            else:
                return np.zeros((features.shape[0], 1))

    def save(self, filepath: str) -> None:
        """
        Save the preprocessor to file.

        Args:
            filepath: Path to save the preprocessor
        """
        preprocessor_state = {
            'normalization': self.normalization,
            'feature_engineering': self.feature_engineering,
            'scalers': self.scalers,
            'feature_names': self.feature_names,
            'target_names': self.target_names,
            'fitted': self.fitted
        }
        joblib.dump(preprocessor_state, filepath)

    @classmethod
    def load(cls, filepath: str) -> 'WellLogPreprocessor':
        """
        Load preprocessor from file.

        Args:
            filepath: Path to the saved preprocessor

        Returns:
            Loaded preprocessor instance
        """
        if not os.path.exists(filepath):
            raise FileNotFoundError(f"Preprocessor file not found: {filepath}")

        state = joblib.load(filepath)
        preprocessor = cls(
            normalization=state['normalization'],
            feature_engineering=state['feature_engineering']
        )
        preprocessor.scalers = state['scalers']
        preprocessor.feature_names = state['feature_names']
        preprocessor.target_names = state['target_names']
        preprocessor.fitted = state['fitted']

        return preprocessor


def split_data(features: np.ndarray, targets: np.ndarray,
              train_ratio: float = 0.7, val_ratio: float = 0.15,
              random_state: int = 42) -> Tuple[np.ndarray, ...]:
    """
    Split data into train, validation, and test sets.

    Args:
        features: Input features
        targets: Target values
        train_ratio: Ratio of training data
        val_ratio: Ratio of validation data
        random_state: Random seed for reproducibility

    Returns:
        Tuple of (train_features, val_features, test_features, train_targets, val_targets, test_targets)
    """
    np.random.seed(random_state)
    n_samples = len(features)
    indices = np.random.permutation(n_samples)

    train_size = int(train_ratio * n_samples)
    val_size = int(val_ratio * n_samples)

    train_indices = indices[:train_size]
    val_indices = indices[train_size:train_size + val_size]
    test_indices = indices[train_size + val_size:]

    return (
        features[train_indices], features[val_indices], features[test_indices],
        targets[train_indices], targets[val_indices], targets[test_indices]
    )


def apply_log_transforms(data: pd.DataFrame, config: dict) -> pd.DataFrame:
    """Apply log10 transform to specified features."""
    log_features = config["data"].get("apply_log_transform", [])

    for feature in log_features:
        if feature in data.columns:
            # Handle zero/negative values by clipping
            data[feature] = np.log10(data[feature].clip(lower=1e-6))
            print(f"Applied log10 transform to {feature}")

    return data